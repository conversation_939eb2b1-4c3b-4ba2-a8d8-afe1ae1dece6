import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/controllers/base_search_bar_controller.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/routes/other.dart';

class BaseController<T extends FeatureSharedState> extends GetxController {
  final T state;

  final AuthService authService;
  late final AppNavigationController appNavigator;
  BaseController(this.state) : authService = Get.find() {
    try {
      appNavigator = Get.find<AppNavigationController>();
    } catch (e) {
      debugPrint('AppNavigator not found in BaseController');
    }
  }

  SessionUser get sessionUser {
    final user = authService.sessionUser;
    if (user != null) return user;
    throw Exception('SessionUser is null. User must be logged in to access this controller.');
  }

  bool get isLoggedIn => authService.isLoggedIn;
  AuthApi get authApi => authService.authApi;
  CommentsApi get commentsApi => authService.commentsApi;
  GroupsApi get groupsApi => authService.groupsApi;
  GroupMembershipsApi get groupMembershipsApi => authService.groupMembershipsApi;
  HobbiesApi get hobbiesApi => authService.hobbiesApi;
  HomeApi get homeApi => authService.homeApi;
  IventsApi get iventsApi => authService.iventsApi;
  IventCollabsApi get iventCollabsApi => authService.iventCollabsApi;
  LocationsApi get locationsApi => authService.locationsApi;
  MapboxApi get mapboxApi => authService.mapboxApi;
  MemoriesApi get memoriesApi => authService.memoriesApi;
  NotificationsApi get notificationsApi => authService.notificationsApi;
  PagesApi get pagesApi => authService.pagesApi;
  PageBlacklistsApi get pageBlacklistsApi => authService.pageBlacklistsApi;
  PageMembershipsApi get pageMembershipsApi => authService.pageMembershipsApi;
  SquadMembershipsApi get squadMembershipsApi => authService.squadMembershipsApi;
  UniversitiesApi get universitiesApi => authService.universitiesApi;
  UsersApi get usersApi => authService.usersApi;
  UserRelationshipsApi get userRelationshipsApi => authService.userRelationshipsApi;
  VibesApi get vibesApi => authService.vibesApi;

  GlobalSharedState get globalState => authService.state;

  @mustCallSuper
  void onInitAsync() async {}

  @mustCallSuper
  void onCloseAsync() async {}

  @override
  void onInit() {
    super.onInit();
    onInitAsync();
  }

  @override
  void onClose() {
    onCloseAsync();
    super.onClose();
  }

  void goToSomethingWentWrongPage() => Get.toNamed(OtherPages.somethingWentWrong);

  bool isLoading([String? tag]) => state.isLoading(tag);

  void handleError(dynamic e, {StackTrace? st, String? customMessage}) {
    Get.defaultDialog(
      title: 'Hata',
      middleText: e.toString(),
      backgroundColor: AppColors.primary,
      titleStyle: AppTextStyles.size20Bold.copyWith(color: AppColors.white),
      middleTextStyle: AppTextStyles.size16Regular.copyWith(color: AppColors.white),
      barrierDismissible: true,
      radius: AppDimensions.radiusS,
      confirm: ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: AppColors.white),
        onPressed: Get.back, // close the dialog
        child: Text('Tamam', style: AppTextStyles.size16Bold.copyWith(color: AppColors.primary)),
      ),
    );
    debugPrint('Exception: $e');
  }

  Future<void> runSafe(
    Future<void> Function() action, {
    String? tag,
    String? errorMessage,
    VoidCallback? onError,
    VoidCallback? onFinally,
    VoidCallback? onSuccess,
  }) async {
    if (state.isLoading(tag)) return;
    try {
      state.setLoadingSafe(true, tag);
      await action();
      onSuccess?.call();
    } catch (e, st) {
      handleError(e, st: st, customMessage: errorMessage);
      onError?.call();
    } finally {
      state.setLoadingSafe(false, tag);
      onFinally?.call();
    }
  }
}

abstract class BaseControllerWithSearch<T extends FeatureSharedState> extends BaseController<T> {
  BaseControllerWithSearch(T state) : super(state);

  late final BaseSearchBarController _baseSearchBarController;

  String get tag => runtimeType.toString();
  TextEditingController get textController => _baseSearchBarController.textController;
  String get searchText => _baseSearchBarController.text;
  bool get isQueryEmpty => _baseSearchBarController.isQueryEmpty;
  bool get isSearching => state.isLoading(tag);

  // Must be implemented by the subclass
  bool get isResultsEmpty;
  InitialSearchBehavior get initialSearchBehavior;
  Future<void> onSearch([String? query]);

  @mustCallSuper
  @override
  void onInitAsync() async {
    super.onInitAsync();
    _baseSearchBarController = BaseSearchBarController(safeSearch, initialSearchBehavior);
    await _baseSearchBarController.initialize();
  }

  @mustCallSuper
  @override
  void onCloseAsync() {
    _baseSearchBarController.close();
    super.onCloseAsync();
  }

  Future<void> safeSearch([String? query]) async {
    await runSafe(tag: tag, () async => await onSearch(query));
  }

  void clearSearch() => _baseSearchBarController.clearSearch();
  void refreshResults() => onSearch(searchText);
}
