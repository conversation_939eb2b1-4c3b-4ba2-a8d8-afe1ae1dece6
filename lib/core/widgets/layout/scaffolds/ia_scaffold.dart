import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/navigation/ia_top_bar.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_bottom_navigation_bar.dart';

class IaScaffold extends StatelessWidget {
  final EdgeInsetsGeometry? bodyPadding;
  final Widget? drawer;
  final Widget? appBar;
  final Widget body;
  final Color? backgroundColor;
  final Widget? floatingActionButton;
  final IaFloatingActionButton? actionButton;
  final IaBottomNavigationBar? bottomNavigationBar;
  final VoidCallback? onRefresh;
  final bool safeArea;

  const IaScaffold._({
    super.key,
    this.bodyPadding,
    this.drawer,
    this.appBar,
    required this.body,
    this.floatingActionButton,
    this.actionButton,
    this.bottomNavigationBar,
    this.backgroundColor,
    this.onRefresh,
    this.safeArea = true,
  });

  static const EdgeInsetsGeometry _defaultBodyPadding = const EdgeInsets.all(0);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.background,
      drawer: drawer,
      body: safeArea ? SafeArea(child: _buildBody()) : _buildBody(),
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: bottomNavigationBar,
    );
  }

  Future<void> _onRefresh() async {
    print('Refresh called');
    onRefresh?.call();
  }

  Widget _buildBody() {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: Column(
        children: [
          if (appBar != null) appBar!,
          Expanded(
            child: Padding(
              padding: bodyPadding ?? _defaultBodyPadding,
              child: body,
            ),
          ),
          if (actionButton != null) actionButton!,
        ],
      ),
    );
  }

  static IaScaffold basic({
    Key? key,
    EdgeInsetsGeometry? bodyPadding,
    Widget? drawer,
    required Widget appBar,
    required Widget body,
    Widget? floatingActionButton,
    IaFloatingActionButton? actionButton,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold._(
      key: key,
      drawer: drawer,
      bodyPadding: bodyPadding,
      body: body,
      appBar: appBar,
      floatingActionButton: floatingActionButton,
      actionButton: actionButton,
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold empty({
    Key? key,
    EdgeInsetsGeometry? bodyPadding,
    required Widget body,
    Widget? floatingActionButton,
    IaFloatingActionButton? actionButton,
    IaBottomNavigationBar? bottomNavigationBar,
    Color? backgroundColor,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold._(
      key: key,
      bodyPadding: bodyPadding,
      body: body,
      floatingActionButton: floatingActionButton,
      actionButton: actionButton,
      bottomNavigationBar: bottomNavigationBar,
      backgroundColor: backgroundColor,
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold search({
    Key? key,
    required Widget body,
    required String title,
    required TextEditingController textEditingController,
    String? searchBarLabelText,
    Widget? trailing,
    Widget? floatingActionButton,
    IaFloatingActionButton? actionButton,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold._(
      key: key,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: IaSearchScreen(
        textController: textEditingController,
        searchBarLabelText: searchBarLabelText,
        body: body,
      ),
      appBar: IaTopBar.basic(title: title, trailing: trailing),
      floatingActionButton: floatingActionButton,
      actionButton: actionButton,
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold onboarding({
    Key? key,
    required Widget body,
  }) {
    return IaScaffold.basic(
      key: key,
      body: body,
      appBar: IaTopBar.onboarding(key: key),
    );
  }

  static IaScaffold auth({
    Key? key,
    required Widget body,
  }) {
    return IaScaffold.basic(
      key: key,
      body: body,
      appBar: IaTopBar.auth(key: key),
    );
  }

  static IaScaffold noSearch({
    Key? key,
    EdgeInsetsGeometry? bodyPadding,
    required Widget body,
    String? title,
    Widget? child,
    Widget? trailing,
    Widget? floatingActionButton,
    IaFloatingActionButton? actionButton,
    bool showBackButton = true,
    bool showDivider = true,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold.basic(
      key: key,
      bodyPadding: bodyPadding,
      body: body,
      appBar: IaTopBar.basic(
        key: key,
        title: title,
        child: child,
        showBackButton: showBackButton,
        showDivider: showDivider,
        trailing: trailing,
      ),
      floatingActionButton: floatingActionButton,
      actionButton: actionButton,
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold home({
    Key? key,
    required Widget body,
    IaBottomNavigationBar? bottomNavigationBar,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold.empty(
      key: key,
      body: body,
      bottomNavigationBar: bottomNavigationBar,
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold business({
    Key? key,
    EdgeInsetsGeometry? bodyPadding,
    required Widget body,
    required String title,
    required String subtitle,
    bool safeArea = true,
    VoidCallback? onRefresh,
  }) {
    return IaScaffold.basic(
      key: key,
      bodyPadding: bodyPadding,
      body: body,
      appBar: IaTopBar.business(
        key: key,
        title: title,
        subtitle: subtitle,
      ),
      safeArea: safeArea,
      onRefresh: onRefresh,
    );
  }

  static IaScaffold loadingBlack({Key? key}) {
    return IaScaffold._(
      key: key,
      appBar: IaTopBar.basic(key: key, showDivider: false, backButtonColor: AppColors.white),
      body: IaLoadingIndicator.white,
      backgroundColor: AppColors.black,
    );
  }

  static IaScaffold loading({Key? key}) {
    return IaScaffold._(
      key: key,
      appBar: IaTopBar.basic(key: key, showDivider: false),
      body: const IaLoadingIndicator(),
    );
  }
}
