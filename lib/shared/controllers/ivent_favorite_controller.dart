import 'package:ivent_app/core/controllers/base_controller.dart';

class IventFavoriteController<T extends BaseController> {
  final T controller;
  final bool Function(String iventId) localGetter;

  IventFavoriteController(this.controller, this.localGetter);

  bool isFavorited(String iventId) {
    final isGloballyFavorited = controller.globalState.getIventFavoriteUpdate(iventId);
    final isLocallyFavorited = localGetter(iventId);
    return isGloballyFavorited ?? isLocallyFavorited;
  }

  Future<void> toggleFavorite(String iventId) async {
    await controller.runSafe(tag: 'toggleFavorite', () async {
      if (isFavorited(iventId)) {
        await unfavoriteIvent(iventId);
      } else {
        await favoriteIvent(iventId);
      }
    });
  }

  Future<void> favoriteIvent(String iventId) async {
    await controller.iventsApi.favoriteIventByIventId(iventId);
    controller.globalState.updateIventFavorite(iventId, true);
  }

  Future<void> unfavoriteIvent(String iventId) async {
    await controller.iventsApi.unfavoriteIventByIventId(iventId);
    controller.globalState.updateIventFavorite(iventId, false);
  }
}
