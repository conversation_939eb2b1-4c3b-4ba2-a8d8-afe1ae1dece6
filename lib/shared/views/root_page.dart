import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

class RootPage extends StatefulWidget {
  final String route;
  final int id;
  final Map<String, String>? parameters;

  const RootPage(
    this.route, {
    super.key,
    required this.id,
    this.parameters,
  });

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _navigateToNextScreen());
  }

  Future<void> _navigateToNextScreen() async {
    await Get.offNamed(widget.route, id: widget.id, parameters: widget.parameters);
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppColors.white,
      body: SizedBox.shrink(),
    );
  }
}
