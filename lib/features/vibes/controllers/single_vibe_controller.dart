import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';

/// Controller for managing a single vibe page
///
/// Handles loading and navigation of individual vibes with
/// next/previous functionality and video playback management.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class SinglePageVibeController extends BaseVibesController {
  final String vibeId;

  SinglePageVibeController(this.vibeId, VibesSharedState state) : super(state);

  // Observable properties
  final _singleVibe = Rx<Vibe?>(null);

  // Getters
  Vibe? get singleVibe => _singleVibe.value;

  // Setters
  set singleVibe(Vibe? value) => _singleVibe.value = value;

  // Lifecycle methods

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    try {
      debugPrint('SinglePageVibeController initialized for vibe: $vibeId');

      // Load single vibe data
      await _loadSingleVibe();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onCloseAsync() {
    try {
      // Stop video if playing
      stopVideo();

      debugPrint('SinglePageVibeController cleaned up for vibe: $vibeId');
    } catch (e) {
      debugPrint('Error cleaning up SinglePageVibeController: $e');
    }

    super.onCloseAsync();
  }

  // Navigation methods with proper error handling

  /// Navigates back to vibes page
  void goBack() => Get.back();

  /// Navigates to vibes page
  void goToVibesPage() {
    try {
      Get.offNamed('/vibes_page');
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to vibe upload page
  void navigateToVibeUpload() {
    try {
      Get.toNamed('/vibe_upload_page', id: Get.find<AppNavigationController>().currentIndex);
    } catch (e) {
      handleError(e);
    }
  }

  /// Shares the current vibe
  void shareVibe() {
    try {
      // TODO: Implement share functionality
      debugPrint('Sharing vibe: $vibeId');
    } catch (e) {
      handleError(e);
    }
  }

  /// Downloads the current vibe
  void downloadVibe() {
    try {
      // TODO: Implement download functionality
      debugPrint('Downloading vibe: $vibeId');
    } catch (e) {
      handleError(e);
    }
  }

  // Private methods

  /// Loads single vibe data by ID
  Future<void> _loadSingleVibe() async {
    await runSafe(
      () async {
        await _loadVibeById(vibeId);
      },
      tag: 'singleVibe',
    );
  }

  // Public methods

  /// Stops video playback if current vibe is a video
  Future<void> stopVideo() async {
    if (singleVibe == null || !singleVibe!.isVideo) return;
    singleVibe!.video!.pause();
  }

  /// Navigates to the next vibe if available
  Future<void> getNextVibe() async {
    if (singleVibe?.content.nextVibeId == null) return;
    await _loadVibeById(singleVibe!.content.nextVibeId!);
  }

  /// Navigates to the previous vibe if available
  Future<void> getPreviousVibe() async {
    if (singleVibe?.content.previousVibeId == null) return;
    await _loadVibeById(singleVibe!.content.previousVibeId!);
  }

  // Private methods

  /// Loads a vibe by its ID and updates the current vibe
  Future<void> _loadVibeById(String targetVibeId) async {
    try {
      // Pause current video if playing
      if (singleVibe?.isVideo == true) {
        singleVibe!.video!.pause();
      }

      final vibeData = await vibesApi.getByVibeId(targetVibeId);
      if (vibeData == null) return;

      singleVibe = _createVibeFromData(vibeData);

      // Initialize and play video if it's a video vibe
      if (singleVibe!.isVideo) {
        await _loadVideo();
      }
    } catch (error) {
      handleVibesError(error, 'Vibe yüklenirken bir hata oluştu.');
    }
  }

  /// Creates a Vibe object from API response data
  Vibe _createVibeFromData(GetVibeByVibeIdReturn vibeData) {
    return Vibe(
      content: VibeItem(
        vibeId: vibeData.vibeId,
        vibeFolderId: vibeData.vibeFolderId,
        mediaUrl: vibeData.mediaUrl,
        mediaFormat: vibeData.mediaFormat,
        thumbnailUrl: vibeData.thumbnailUrl,
        caption: vibeData.caption,
        creatorId: vibeData.creatorId,
        creatorType: vibeData.creatorType,
        creatorUsername: vibeData.creatorUsername,
        creatorAvatarUrl: vibeData.creatorAvatarUrl,
        iventId: vibeData.iventId,
        iventName: vibeData.iventName,
        memberCount: vibeData.memberCount,
        likeCount: vibeData.likeCount,
        commentCount: vibeData.commentCount,
        nextVibeId: vibeData.nextVibeId,
        previousVibeId: vibeData.previousVibeId,
        vibeIndex: vibeData.vibeIndex,
        vibeCount: vibeData.vibeCount,
        createdAt: vibeData.createdAt,
      ),
      vibeFolderId: vibeData.vibeFolderId,
      videoManager: vibeData.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );
  }

  /// Loads and initializes video for the current vibe
  Future<void> _loadVideo() async {
    await singleVibe!.video?.initializeVideo(cacheManager);
    singleVibe!.video?.play();
  }
}
