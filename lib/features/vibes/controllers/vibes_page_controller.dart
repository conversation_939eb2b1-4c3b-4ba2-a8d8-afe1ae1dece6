import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';
import 'package:ivent_app/features/vibes/models/vibe_folder.dart';

/// Controller for managing the main Vibes page
///
/// Handles loading, pagination, and navigation of Vibes content.
/// Manages video playback, caching, and infinite scroll functionality.
/// Provides methods for navigating between vibes and managing video state.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class VibesPageController extends BaseVibesController {
  // Constants
  static const int _maxCachedItems = 30;
  static const int _preloadCount = 2;
  static const int _initialLoadLimit = 6;

  // Reactive state
  final _vibeFolders = <VibeFolder>[].obs;
  final _currentVibePage = 0.obs;
  final _isScrollContinuable = true.obs;
  final _isLoadingMore = false.obs;

  // Constructor
  VibesPageController(VibesSharedState state) : super(state);

  // Lifecycle methods

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    try {
      debugPrint('VibesPageController initialized');

      // Load initial vibes data
      await _loadInitialVibes();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onCloseAsync() {
    try {
      // Dispose all vibe folders
      for (final folder in _vibeFolders) {
        folder.dispose();
      }

      debugPrint('VibesPageController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up VibesPageController: $e');
    }

    super.onCloseAsync();
  }

  // Getters
  List<VibeFolder> get vibeFolders => _vibeFolders;
  int get currentVibePage => _currentVibePage.value;
  bool get isScrollContinuable => _isScrollContinuable.value;
  bool get isLoadingMore => _isLoadingMore.value;

  VibeFolder get currentVibeFolder => vibeFolders[currentVibePage];
  Map<int, Vibe> get currentVibelets => currentVibeFolder.vibelets;
  Vibe get currentVibelet => currentVibeFolder.currentVibelet;
  int get currentVibeletIndex => currentVibeFolder.currentVibeletIndex;

  // Setters
  set vibeFolders(List<VibeFolder> value) => _vibeFolders.assignAll(value);
  set currentVibePage(int value) => _currentVibePage.value = value;
  set isScrollContinuable(bool value) => _isScrollContinuable.value = value;
  set isLoadingMore(bool value) => _isLoadingMore.value = value;

  // Navigation methods with proper error handling

  /// Navigates to single vibe page
  void navigateToSingleVibe(String vibeId) {
    try {
      Get.toNamed('/single_vibe_page', parameters: {'vibeId': vibeId}, id: Get.find<AppNavigationController>().currentIndex);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to vibe upload page
  void navigateToVibeUpload() {
    try {
      Get.toNamed('/vibe_upload_page', id: Get.find<AppNavigationController>().currentIndex);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to camera page
  void navigateToCamera() {
    try {
      Get.toNamed('/camera_page', id: Get.find<AppNavigationController>().currentIndex);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates to gallery page
  void navigateToGallery() {
    try {
      Get.toNamed('/gallery_page', id: Get.find<AppNavigationController>().currentIndex);
    } catch (e) {
      handleError(e);
    }
  }

  /// Navigates back to home
  void goToHome() => Get.back();

  /// Refreshes vibes data
  Future<void> refreshVibes() async {
    try {
      _vibeFolders.clear();
      _currentVibePage.value = 0;
      _isScrollContinuable.value = true;
      await _loadInitialVibes();
    } catch (e) {
      handleError(e);
    }
  }

  // Private methods

  /// Loads initial vibes data
  Future<void> _loadInitialVibes() async {
    await runSafe(
      () async {
        await loadMoreVibes();
      },
      tag: 'initialVibes',
    );
  }

  // Public methods

  /// Loads more vibes for infinite scroll
  Future<void> loadMoreVibes() async {
    if (isLoadingMore || !isScrollContinuable) return;

    try {
      isLoadingMore = true;

      final newVibeFolders = await vibesApi.getVibes(
        limit: _initialLoadLimit,
        page: vibeFolders.length ~/ _initialLoadLimit + 1, // TODO: Add random seed suggestions rather than pagination
      );

      if (newVibeFolders == null || newVibeFolders.vibes.isEmpty) {
        isScrollContinuable = false;
        isLoadingMore = false;
        return;
      }

      _addNewVibeFolders(newVibeFolders.vibes);
      _manageCacheSize();
      _preloadVibes(currentVibePage);

      isLoadingMore = false;
    } catch (e) {
      handleVibesError(e, 'Yeni Vibes yüklenirken bir hata oluştu.');
      isLoadingMore = false;
    }
  }

  /// Navigates to the previous vibe in the current folder
  Future<void> getPreviousVibe() async {
    final previousVibeId = currentVibelet.content.previousVibeId;
    if (previousVibeId == null) return;

    try {
      currentVibelet.video?.pause();

      if (currentVibeFolder.vibeletIds.contains(previousVibeId)) {
        currentVibeFolder.setCurrentVibeletById(previousVibeId);
        await _loadVideo();
        return;
      }

      await _loadAndAddVibe(previousVibeId);
    } catch (e) {
      handleVibesError(e, 'Önceki Vibe yüklenirken bir hata oluştu.');
    }
  }

  /// Navigates to the next vibe in the current folder
  Future<void> getNextVibe() async {
    final nextVibeId = currentVibelet.content.nextVibeId;
    if (nextVibeId == null) return;

    try {
      currentVibelet.video?.pause();

      if (currentVibeFolder.vibeletIds.contains(nextVibeId)) {
        currentVibeFolder.setCurrentVibeletById(nextVibeId);
        await _loadVideo();
        return;
      }

      await _loadAndAddVibe(nextVibeId);
    } catch (e) {
      handleVibesError(e, 'Sonraki Vibe yüklenirken bir hata oluştu.');
    }
  }

  // Private methods

  /// Creates a VibeFolder from VibeItem
  VibeFolder _createVibeFolder(VibeItem vibeItem) {
    return VibeFolder(
      vibeFolderId: vibeItem.vibeFolderId,
      thumbnailUrl: vibeItem.thumbnailUrl,
      initialVibeIndex: vibeItem.vibeIndex,
      vibelets: {
        vibeItem.vibeIndex: Vibe(
          content: vibeItem,
          vibeFolderId: vibeItem.vibeFolderId,
          videoManager: vibeItem.mediaFormat == MediaFormatEnum.video ? videoManager : null,
        )
      },
    );
  }

  /// Adds new vibe folders to the list
  void _addNewVibeFolders(List<VibeItem> newVibes) {
    vibeFolders.addAll(newVibes.map((val) => _createVibeFolder(val)).toList());
  }

  /// Manages cache size to prevent memory issues
  void _manageCacheSize() {
    if (vibeFolders.length > _maxCachedItems) {
      final itemsToRemove = vibeFolders.length - _maxCachedItems;
      for (int i = 0; i < itemsToRemove; i++) {
        vibeFolders[i].dispose(); // Clean up video controllers
        // TODO: Maybe there is no need, because video manager handles it, so check it later
      }
      vibeFolders.removeRange(0, itemsToRemove);
      currentVibePage = max(0, currentVibePage - itemsToRemove);
    }
  }

  /// Preloads vibes around the current index for smooth scrolling
  void _preloadVibes(int centerIndex) {
    final startIndex = max(0, centerIndex - _preloadCount);
    final endIndex = min(vibeFolders.length - 1, centerIndex + _preloadCount);

    for (int i = startIndex; i <= endIndex; i++) {
      final vibe = vibeFolders[i].currentVibelet;
      vibe.video?.initializeVideo(cacheManager);
    }
  }

  /// Loads and adds a vibe by ID to the current folder
  Future<void> _loadAndAddVibe(String vibeId) async {
    final vibe = await vibesApi.getByVibeId(vibeId);
    if (vibe == null) return;

    final newVibelet = Vibe(
      content: VibeItem(
        vibeId: vibe.vibeId,
        vibeFolderId: vibe.vibeFolderId,
        mediaUrl: vibe.mediaUrl,
        mediaFormat: vibe.mediaFormat,
        thumbnailUrl: vibe.thumbnailUrl,
        caption: vibe.caption,
        creatorId: vibe.creatorId,
        creatorType: vibe.creatorType,
        creatorUsername: vibe.creatorUsername,
        creatorAvatarUrl: vibe.creatorAvatarUrl,
        iventId: vibe.iventId,
        iventName: vibe.iventName,
        dates: vibe.dates,
        memberCount: vibe.memberCount,
        memberFirstnames: vibe.memberFirstnames,
        likeCount: vibe.likeCount,
        commentCount: vibe.commentCount,
        nextVibeId: vibe.nextVibeId,
        previousVibeId: vibe.previousVibeId,
        vibeIndex: vibe.vibeIndex,
        vibeCount: vibe.vibeCount,
        createdAt: vibe.createdAt,
      ),
      vibeFolderId: vibe.vibeFolderId,
      videoManager: vibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );

    currentVibeFolder.addNewVibelet(newVibelet);
    await _loadVideo();
  }

  /// Loads video for the current vibe
  Future<void> _loadVideo() async {
    await currentVibelet.video?.initializeVideo(cacheManager);
  }
}
