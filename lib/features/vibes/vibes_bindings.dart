import 'package:get/get.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';

class VibesBindings implements Bindings {
  @override
  void dependencies() {
    final state = Get.put(VibesSharedState(), permanent: true);

    Get.lazyPut(() => VibesPageController(state), fenix: true);
    Get.lazyPut(() => VibeUploadController(state), fenix: true);
  }
}
