import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_ivent_tile.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';

class ChooseIvent extends GetView<VibeUploadController> {
  const ChooseIvent({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'iVent Seç',
      textEditingController: controller.textController,
      body: Obx(() => IaSearchResultsBuilder(
            entityName: 'iVent',
            isSearching: controller.isSearching,
            isResultsEmpty: controller.isResultsEmpty,
            isQueryEmpty: controller.isQueryEmpty,
            initialSearchBehavior: InitialSearchBehavior.loaded,
            builder: (context) => ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: controller.iventsResult!.iventCount,
              itemBuilder: (context, index) {
                final ivent = controller.iventsResult!.ivents[index];
                return _SearchResult(ivent: ivent);
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            ),
          )),
      floatingActionButton: Obx(() => IaFloatingActionButton(
            onPressed: controller.uploadVibe,
            text: controller.selectedVibeFolderId != null ? 'Vibe Paylaş' : "iVent'i Seç",
            isLoading: controller.isLoading('uploadVibe'),
            isEnabled: controller.selectedVibeFolderId != null,
          )),
    );
  }
}

class _SearchResult extends GetView<VibeUploadController> {
  const _SearchResult({
    required this.ivent,
  });

  final IventListItem ivent;

  @override
  Widget build(BuildContext context) {
    return Obx(() => IaIventTile(
          imageUrl: ivent.thumbnailUrl,
          iventName: ivent.iventName,
          locationName: ivent.locationName,
          date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
          memberAvatarUrls: ivent.memberAvatarUrls,
          memberCount: ivent.memberCount,
          memberNames: ivent.memberFirstnames,
          isOrganizerUser: ivent.creatorType != IventCreatorTypeEnum.distributor,
          organizerName: ivent.creatorUsername,
          organizerAvatarUrl: ivent.creatorImageUrl,
          viewType: ivent.viewType,
          onTap: () => controller.selectIvent(ivent.vibeFolderId),
          isSelected: controller.selectedVibeFolderId == ivent.vibeFolderId,
        ));
  }
}
