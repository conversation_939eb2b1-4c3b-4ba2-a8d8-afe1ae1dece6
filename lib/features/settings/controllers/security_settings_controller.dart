import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/settings/controllers/settings_state_manager.dart';

/// Security settings controller
///
/// Manages security-related settings including email updates, phone updates,
/// education status, notification preferences, and account security features.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class SecuritySettingsController extends BaseController<SettingsSharedState> {
  /// Constructor with dependency injection
  SecuritySettingsController() : super(Get.find<SettingsSharedState>());

  // Lifecycle methods

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    try {
      debugPrint('SecuritySettingsController initialized');

      // Load security settings data
      await _loadSecurityData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onCloseAsync() {
    try {
      debugPrint('SecuritySettingsController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up SecuritySettingsController: $e');
    }

    super.onCloseAsync();
  }

  // Education status - BACKEND'den geliyor
  final RxString educationStatus = 'unverified'.obs;

  // Notification settings - BACKEND'den geliyor
  final RxBool pushNotifications = false.obs;
  final RxBool emailNotifications = false.obs;
  final RxBool smsNotifications = false.obs;
  final RxBool eventReminders = false.obs;
  final RxBool socialNotifications = false.obs;
  final RxBool marketingEmails = false.obs;

  // Security settings - BACKEND'den geliyor
  final RxBool twoFactorAuth = false.obs;
  final RxBool loginAlerts = false.obs;

  // Loading states
  final RxBool isLoadingNotifications = false.obs;

  // Private methods

  /// Loads security settings data
  Future<void> _loadSecurityData() async {
    await runSafe(
      () async {
        await loadAllSettingsFromBackend();
      },
      tag: 'securityData',
    );
  }

  /// BACKEND ENTEGRASYON: Tüm ayarları backend'den yükle
  Future<void> loadAllSettingsFromBackend() async {
    try {
      // 1. User profile bilgilerini yükle (education status için)
      await _loadUserProfile();

      // 2. Notification settings'i yükle
      await _loadNotificationSettings();

      // 3. Security settings'i yükle
      await _loadSecuritySettings();
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error loading settings from backend: $e');
      Get.snackbar(
        'Hata',
        'Ayarlar yüklenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {}
  }

  /// BACKEND: User profile bilgilerini yükle (sadece education status)
  Future<void> _loadUserProfile() async {
    try {
      final userProfile = await usersApi.getByUserId(sessionUser.sessionId);

      if (userProfile != null) {
        // Education status - UserRole'den çıkarsayabiliriz
        if (userProfile.userRole.value.contains('student')) {
          educationStatus.value = 'student';
        } else if (userProfile.userRole.value.contains('grad')) {
          educationStatus.value = 'grad';
        } else {
          educationStatus.value = 'unverified';
        }

        debugPrint('✅ [SecuritySettings] Education status loaded: ${educationStatus.value}');
      }
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error loading user profile: $e');
      educationStatus.value = 'unverified';
    }
  }

  /// BACKEND: Notification settings'i yükle
  Future<void> _loadNotificationSettings() async {
    try {
      // TODO: Backend'de notification settings GET API'si hazır olduğunda buraya eklenecek
      debugPrint('🔔 [SecuritySettings] Loading notification settings from backend...');

      // Şimdilik default değerler (backend hazır olduğunda silinecek)
      pushNotifications.value = true;
      emailNotifications.value = true;
      smsNotifications.value = false;
      eventReminders.value = true;
      socialNotifications.value = true;
      marketingEmails.value = false;
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error loading notification settings: $e');
    }
  }

  /// BACKEND: Security settings'i yükle
  Future<void> _loadSecuritySettings() async {
    try {
      // TODO: Backend'de security settings GET API'si hazır olduğunda buraya eklenecek
      debugPrint('🔒 [SecuritySettings] Loading security settings from backend...');

      // Şimdilik default değerler (backend hazır olduğunda silinecek)
      twoFactorAuth.value = false;
      loginAlerts.value = true;
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error loading security settings: $e');
    }
  }

  /// BACKEND ENTEGRE: Update phone number (sadece update, display yok)
  Future<void> updatePhoneNumber(String newPhoneNumber) async {
    if (newPhoneNumber.trim().isEmpty) {
      Get.snackbar(
        'Hata',
        'Lütfen geçerli bir telefon numarası girin',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final dto = UpdatePhoneNumberByUserIdDto(
        newPhoneNumber: newPhoneNumber.trim(),
      );

      // BACKEND UPDATE
      await usersApi.updatePhoneNumberByUserId(sessionUser.sessionId, dto);

      Get.snackbar(
        'Başarılı',
        'Telefon numarası güncellendi',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Phone number updated successfully');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error updating phone: $e');
      Get.snackbar(
        'Hata',
        'Telefon numarası güncellenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// BACKEND ENTEGRE: Update email (sadece update, display yok)
  Future<void> updateEmail(String newEmail) async {
    if (!GetUtils.isEmail(newEmail.trim())) {
      Get.snackbar(
        'Hata',
        'Lütfen geçerli bir e-posta adresi girin',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final dto = UpdateEmailByUserIdDto(
        newEmail: newEmail.trim(),
      );

      // BACKEND UPDATE
      await usersApi.updateEmailByUserId(sessionUser.sessionId, dto);

      Get.snackbar(
        'Başarılı',
        'E-posta adresi güncellendi. Doğrulama kodu gönderildi.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Email updated successfully');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error updating email: $e');
      Get.snackbar(
        'Hata',
        'E-posta adresi güncellenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// BACKEND ENTEGRE: Send verification email
  Future<void> sendVerificationEmail() async {
    try {
      // BACKEND API CALL
      await usersApi.sendVerificationEmail(sessionUser.sessionId);

      Get.snackbar(
        'Başarılı',
        'Doğrulama kodu e-posta adresinize gönderildi',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Verification email sent successfully');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error sending verification email: $e');
      Get.snackbar(
        'Hata',
        'Doğrulama kodu gönderilirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// BACKEND ENTEGRE: Update education status
  Future<void> updateEducationStatus(String newStatus) async {
    try {
      final dto = UpdateGradByUserIdDto(
        newGrad: UserEduVerificationEnum.values.firstWhere(
          (e) => e.value == newStatus,
          orElse: () => UserEduVerificationEnum.unverified,
        ),
      );

      // BACKEND UPDATE
      await usersApi.updateGradByUserId(sessionUser.sessionId, dto);

      // Local state update
      educationStatus.value = newStatus;

      Get.snackbar(
        'Başarılı',
        'Eğitim durumu güncellendi',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Education status updated to: $newStatus');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error updating education status: $e');
      Get.snackbar(
        'Hata',
        'Eğitim durumu güncellenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// BACKEND ENTEGRE: Notification settings update
  Future<void> updateNotificationSettings() async {
    try {
      isLoadingNotifications.value = true;

      // BACKEND'e gönderilecek tam bildirim ayarları
      final notificationSettings = {
        'pushNotifications': pushNotifications.value,
        'emailNotifications': emailNotifications.value,
        'smsNotifications': smsNotifications.value,
        'eventReminders': eventReminders.value,
        'socialNotifications': socialNotifications.value,
        'marketingEmails': marketingEmails.value,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // BACKEND UPDATE
      await usersApi.updateNotificationsByUserId(sessionUser.sessionId, notificationSettings);

      Get.snackbar(
        'Başarılı',
        'Bildirim ayarları güncellendi',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Notification settings updated: $notificationSettings');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error updating notifications: $e');
      Get.snackbar(
        'Hata',
        'Bildirim ayarları güncellenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingNotifications.value = false;
    }
  }

  /// BACKEND ENTEGRE: Security settings update
  Future<void> updateSecuritySetting(String setting, bool value) async {
    try {
      // Local state update
      switch (setting) {
        case 'twoFactorAuth':
          twoFactorAuth.value = value;
          break;
        case 'loginAlerts':
          loginAlerts.value = value;
          break;
      }

      // BACKEND'e gönderilecek security settings (API hazır olduğunda kullanılacak)
      // ignore: unused_local_variable
      final securitySettings = {
        'twoFactorAuth': twoFactorAuth.value,
        'loginAlerts': loginAlerts.value,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // TODO: Backend'de security settings API'si hazır olduğunda
      // await usersApi.updateSecurityByUserId(sessionUser.sessionId, securitySettings);

      Get.snackbar(
        'Başarılı',
        'Güvenlik ayarı güncellendi',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [SecuritySettings] Security setting updated: $setting = $value');
    } catch (e) {
      debugPrint('❌ [SecuritySettings] Error updating security setting: $e');
      Get.snackbar(
        'Hata',
        'Güvenlik ayarı güncellenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  String getEducationStatusText() {
    switch (educationStatus.value) {
      case 'verified':
      case 'grad':
        return 'Doğrulandı ✓';
      case 'student':
        return 'Öğrenci ✓';
      case 'pending':
        return 'Doğrulama Bekliyor';
      case 'unverified':
      default:
        return 'Doğrulanmadı';
    }
  }

  Color getEducationStatusColor() {
    switch (educationStatus.value) {
      case 'verified':
      case 'grad':
      case 'student':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'unverified':
      default:
        return Colors.grey;
    }
  }

  /// Refresh all settings from backend
  Future<void> refreshSettings() async {
    await loadAllSettingsFromBackend();
  }

  /// Show phone number update dialog
  void showPhoneUpdateDialog() {
    final TextEditingController phoneController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('Telefon Numarası Güncelle'),
        content: TextField(
          controller: phoneController,
          decoration: const InputDecoration(
            labelText: 'Yeni Telefon Numarası',
            hintText: '+90 555 123 45 67',
          ),
          keyboardType: TextInputType.phone,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              updatePhoneNumber(phoneController.text);
            },
            child: const Text('Güncelle'),
          ),
        ],
      ),
    );
  }

  /// Show email update dialog
  void showEmailUpdateDialog() {
    final TextEditingController emailController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('E-posta Adresi Güncelle'),
        content: TextField(
          controller: emailController,
          decoration: const InputDecoration(
            labelText: 'Yeni E-posta Adresi',
            hintText: '<EMAIL>',
          ),
          keyboardType: TextInputType.emailAddress,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              updateEmail(emailController.text);
            },
            child: const Text('Güncelle'),
          ),
        ],
      ),
    );
  }
}
