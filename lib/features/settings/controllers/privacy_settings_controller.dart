import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/settings/controllers/settings_state_manager.dart';

/// Privacy settings controller
///
/// Manages privacy-related settings including blocked users, account privacy,
/// and privacy preferences. Handles blocking/unblocking users and privacy toggles.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class PrivacySettingsController extends BaseController<SettingsSharedState> {
  /// Constructor with dependency injection
  PrivacySettingsController() : super(Get.find<SettingsSharedState>());

  // Lifecycle methods

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    try {
      debugPrint('PrivacySettingsController initialized');

      // Load privacy settings data
      await _loadPrivacyData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onCloseAsync() {
    try {
      debugPrint('PrivacySettingsController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up PrivacySettingsController: $e');
    }

    super.onCloseAsync();
  }

  // BACKEND DATA: Blocked users list - TEK ÇALIŞAN API
  final RxList<UserListItem> blockedUsers = <UserListItem>[].obs;
  final RxBool isLoadingBlocked = false.obs;

  // Loading states

  @override
  void onInit() {
    super.onInit();
    loadBlockedUsersFromBackend();
  }

  /// BACKEND ENTEGRASYON: Blocked users'ı backend'den yükle (TEK ÇALIŞAN API)
  Future<void> loadBlockedUsersFromBackend() async {
    try {
      await loadBlockedUsers();
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error loading blocked users from backend: $e');
      Get.snackbar(
        'Hata',
        'Engellenen kullanıcılar yüklenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {}
  }

  /// BACKEND: Blocked users listesini yükle - TEK ÇALIŞAN API
  Future<void> loadBlockedUsers() async {
    try {
      isLoadingBlocked.value = true;

      // BACKEND API CALL
      final response = await userRelationshipsApi.getUserBlocklist();

      if (response != null && response.users.isNotEmpty) {
        // Create a modifiable copy of the list to avoid "fixed-length list" errors
        blockedUsers.value = List<UserListItem>.from(response.users);
        debugPrint('✅ [PrivacySettings] Loaded ${response.users.length} blocked users from backend');
      } else {
        blockedUsers.clear();
        debugPrint('📝 [PrivacySettings] No blocked users found');
      }
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error loading blocked users: $e');
      // Backend API henüz hazır değilse normal, hata gösterme
      blockedUsers.clear();
    } finally {
      isLoadingBlocked.value = false;
    }
  }

  /// BACKEND ENTEGRE: Unblock user - TEK ÇALIŞAN API
  Future<void> unblockUser(String userId, String username) async {
    try {
      // BACKEND API CALL
      await userRelationshipsApi.unblockUserByUserId(userId);

      // Local state update
      print('Unblocking user: $blockedUsers');
      blockedUsers.removeWhere((user) => user.userId == userId);
      print('Blocked user: $blockedUsers');

      Get.snackbar(
        'Başarılı',
        '$username engeli kaldırıldı',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [PrivacySettings] User $username unblocked successfully');
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error unblocking user: $e');
      Get.snackbar(
        'Hata',
        'Engel kaldırılırken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Refresh blocked users from backend
  Future<void> refreshPrivacySettings() async {
    await loadBlockedUsersFromBackend();
  }

  /// Get blocked users count
  int get blockedUsersCount => blockedUsers.length;

  // Private methods

  /// Loads privacy settings data
  Future<void> _loadPrivacyData() async {
    await runSafe(
      () async {
        await loadBlockedUsersFromBackend();
      },
      tag: 'privacyData',
    );
  }
}
