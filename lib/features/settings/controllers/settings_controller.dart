import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_state_manager.dart';
import 'package:ivent_app/features/settings/utils/logout_dialog.dart';
import 'package:ivent_app/routes/settings.dart';
import 'package:url_launcher/url_launcher.dart';

/// Main settings controller
///
/// Handles navigation between different settings pages and manages
/// common settings operations like logout and account deletion.
///
/// Follows the architecture guide's controller patterns with proper BaseController
/// inheritance, initialization order, and comprehensive lifecycle management.
class SettingsController extends BaseController<SettingsSharedState> {
  /// Constructor with dependency injection
  SettingsController() : super(Get.find<SettingsSharedState>());

  // Lifecycle methods

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    try {
      debugPrint('SettingsController initialized');

      // Initialize any required data
      await _loadInitialData();
    } catch (e) {
      handleError(e);
    }
  }

  @override
  void onCloseAsync() {
    try {
      debugPrint('SettingsController cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up SettingsController: $e');
    }

    super.onCloseAsync();
  }

  // Navigation methods
  void goToPrivacySettings() {
    Get.toNamed(SettingsPages.privacySettings, id: Get.find<AppNavigationController>().currentIndex);
  }

  void goToSecuritySettings() {
    Get.toNamed(SettingsPages.securitySettings, id: Get.find<AppNavigationController>().currentIndex);
  }

  void goToSupportPage() {
    Get.toNamed(SettingsPages.support, id: Get.find<AppNavigationController>().currentIndex);
  }

  void goToAboutPage() {
    Get.toNamed(SettingsPages.about, id: Get.find<AppNavigationController>().currentIndex);
  }

  // Auth methods
  void showLogoutDialog() {
    LogoutDialog.show();
  }

  Future<void> deleteAccount() async {
    // TODO: Implement this.
    // await deleteAccount();
    await deleteAccount();
  }

  // External link methods
  Future<void> openWebsite() async {
    await _launchUrl(SettingsConstants.websiteUrl);
  }

  Future<void> openPrivacyPolicy() async {
    await _launchUrl(SettingsConstants.privacyPolicyUrl);
  }

  Future<void> openTermsOfService() async {
    await _launchUrl(SettingsConstants.termsOfServiceUrl);
  }

  // Private methods

  /// Launches URL in external application
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } else {
      await launchUrl(
        uri,
        mode: LaunchMode.platformDefault,
      );
    }
  }

  /// Loads initial data for settings
  Future<void> _loadInitialData() async {
    await runSafe(
      () async {
        // Load any required initial data here
        // For example, load user preferences, settings, etc.
      },
      tag: 'initData',
    );
  }
}
