import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_bottom_navigation_bar.dart';
import 'package:ivent_app/features/side_menu/ia_profile_simple_drawer.dart';
import 'package:ivent_app/routes/app_pages.dart';
import 'package:ivent_app/shared/views/root_page.dart';

class AppNavigation extends GetView<AppNavigationController> {
  const AppNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const IaProfileSimpleDrawer(),
      body: Obx(() {
        final currentIndex = controller.currentIndex;
        return IndexedStack(
          index: currentIndex,
          children: controller.tabs
              .asMap()
              .entries
              .map((entry) => _LazyNavigator(
                    index: entry.key,
                    initialRoute: entry.value.route,
                    parameters: entry.value.parameters,
                    isActive: entry.key == currentIndex,
                  ))
              .toList(),
        );
      }),
      bottomNavigationBar:
          const IaBottomNavigationBar(iconPaths: [AppAssets.house01, AppAssets.bell, AppAssets.user01]),
    );
  }
}

class _LazyNavigator extends StatefulWidget {
  final int index;
  final String initialRoute;
  final Map<String, String>? parameters;
  final bool isActive;

  const _LazyNavigator({
    required this.index,
    required this.initialRoute,
    this.parameters,
    required this.isActive,
  });

  @override
  State<_LazyNavigator> createState() => _LazyNavigatorState();
}

class _LazyNavigatorState extends State<_LazyNavigator> {
  bool isBuilt = false;
  Widget cachedNavigator = const SizedBox.shrink();

  @override
  Widget build(BuildContext context) {
    if (!isBuilt && widget.isActive) {
      isBuilt = true;
      cachedNavigator = _Navigator(
        index: widget.index,
        initialRoute: widget.initialRoute,
        parameters: widget.parameters,
      );
    }
    return cachedNavigator;
  }
}

class _Navigator extends StatelessWidget {
  final int index;
  final String initialRoute;
  final Map<String, String>? parameters;

  const _Navigator({
    required this.index,
    required this.initialRoute,
    this.parameters,
  });

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: Get.nestedKey(index),
      initialRoute: initialRoute,
      onGenerateInitialRoutes: _generateInitialRoutes,
      onGenerateRoute: _generateRoute,
    );
  }

  List<Route> _generateInitialRoutes(NavigatorState navigator, String initialRouteName) {
    return [MaterialPageRoute(builder: (_) => RootPage(initialRouteName, id: index, parameters: parameters))];
  }

  Route _generateRoute(RouteSettings settings) {
    final elements = settings.name?.split('?') ?? [];
    final name = elements.isNotEmpty ? elements[0] : null;
    final params = _getParams(elements.length > 1 ? elements[1] : null);
    final page = _getPage(name);
    Get.parameters = params;
    return GetPageRoute(
      settings: settings,
      page: page.page,
      binding: page.binding,
    );
  }

  Map<String, String> _getParams(String? paramsString) {
    if (paramsString == null) return {};
    final paramsList = paramsString.split('&');

    final Map<String, String> params = {};
    for (var param in paramsList) {
      final keyValue = param.split('=');
      params[keyValue[0]] = keyValue[1];
    }
    return params;
  }

  GetPage _getPage(String? routeName) {
    return AppPages.routes.firstWhere(
      (p) => p.name == routeName,
      orElse: () => AppPages.notFoundPage,
    );
  }
}
