import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_squad_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class IventDetailSquad extends GetView<IventSquadController> {
  final String iventId;

  const IventDetailSquad({Key? key, required this.iventId}) : super(key: key);

  @override
  String? get tag => iventId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: controller.state.iventName,
      textEditingController: controller.textController,
      body: Obx(() {
        final squadsResult = controller.squadsResult;
        return IaSearchResultsBuilder(
          entityName: 'Kullanıcı',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: squadsResult!.userCount,
            itemBuilder: (context, index) {
              final user = squadsResult.users[index];
              return _SearchResult(user: user);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends GetView<AppNavigationController> {
  const _SearchResult({required this.user});

  final UserListItemWithRelationshipStatus user;

  @override
  Widget build(BuildContext context) {
    return IaListTile.withImageUrl(
      avatarUrl: user.avatarUrl,
      title: '@${user.username}',
      onTap: () => Get.toNamed(ProfilePages.userProfile, id: controller.currentIndex, parameters: {'id': user.userId}),
      trailing: SharedButtons.moreVertical(),
    );
  }
}
