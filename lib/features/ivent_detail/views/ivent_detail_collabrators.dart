import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_collab_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class IventDetailCollabrators extends GetView<IventCollabController> {
  final String iventId;

  const IventDetailCollabrators({Key? key, required this.iventId}) : super(key: key);

  @override
  String? get tag => iventId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Paydaşlar',
      textEditingController: controller.textController,
      body: Obx(() {
        final collabsResult = controller.collabsResult;
        return IaSearchResultsBuilder(
          entityName: 'Paydaş',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: collabsResult!.collabCount,
            itemBuilder: (context, index) {
              final collabrator = collabsResult.collabs[index];
              return _SearchResult(collabrator: collabrator);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends GetView<AppNavigationController> {
  const _SearchResult({required this.collabrator});

  final CollabratorListItem collabrator;

  @override
  Widget build(BuildContext context) {
    return IaListTile.withImageUrl(
      avatarUrl: collabrator.collabImageUrl,
      title: '@${collabrator.collabName}',
      onTap: () {
        var routeName;
        switch (collabrator.collabType) {
          case AccountTypeEnum.user:
            routeName = ProfilePages.userProfile;
            break;
          case AccountTypeEnum.page:
            return;
        }
        Get.toNamed(routeName, id: controller.currentIndex, parameters: {'id': collabrator.collabId});
      },
      trailing: SharedButtons.moreVertical(),
    );
  }
}
