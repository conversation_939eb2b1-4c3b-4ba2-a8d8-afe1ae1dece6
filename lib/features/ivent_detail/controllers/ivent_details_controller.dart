import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/shared/controllers/ivent_favorite_controller.dart';

class IventDetailsController extends BaseController<IventDetailSharedState> {
  final _iventPage = Rxn<GetIventPageByIventIdReturn>();
  final _viewType = IventViewTypeEnum.default_.obs;

  late final IventFavoriteController favoriteController;

  IventDetailsController(IventDetailSharedState state) : super(state) {
    final localGetter = (iventId) => iventPage?.isFavorited ?? false;
    favoriteController = IventFavoriteController(this, localGetter);
  }

  GetIventPageByIventIdReturn? get iventPage => _iventPage.value;
  IventViewTypeEnum get viewType => _viewType.value;

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await _loadIventInfo();
  }

  Future<void> _loadIventInfo() async {
    await runSafe(tag: 'loadIventInfo', () async {
      _iventPage.value = await iventsApi.getIventPageByIventId(state.iventId);
      _viewType.value = _iventPage.value!.viewType;
      state.setIventName(_iventPage.value!.iventName);
    });
  }

  void goToCollaboratorsPage() {
    Get.toNamed(IventDetailPages.collaborators, id: appNavigator.currentIndex, parameters: {'iventId': state.iventId});
  }

  void goToParticipantsPage() {
    final memberCount = iventPage?.memberCount ?? 0;
    if (viewType == IventViewTypeEnum.joined && memberCount > 0) {
      Get.toNamed(IventDetailPages.squad, id: appNavigator.currentIndex, parameters: {'iventId': state.iventId});
    }
  }

  bool get isFavorited => favoriteController.isFavorited(state.iventId);
  void toggleFavorite() async => await favoriteController.toggleFavorite(state.iventId);

  void joinIvent() async {
    await runSafe(tag: 'joinIvent', () async {
      await squadMembershipsApi.joinIventAndCreateSquadByIventId(
        state.iventId,
        JoinIventAndCreateSquadByIventIdDto(groupIds: [], userIds: [
          sessionUser.sessionId,
        ]),
      );
      _loadIventInfo();
    });
  }
}
