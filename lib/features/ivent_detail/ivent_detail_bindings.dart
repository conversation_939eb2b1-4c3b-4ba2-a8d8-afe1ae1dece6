import 'package:get/get.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_collab_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_squad_controller.dart';

class IventDetailBindings implements Bindings {
  @override
  void dependencies() {
    final iventId = Get.parameters['iventId']!;
    final state = Get.put(IventDetailSharedState(iventId), tag: iventId);

    Get.lazyPut(() => IventDetailsController(state), tag: iventId, fenix: true);
    Get.lazyPut(() => IventCollabController(state), tag: iventId, fenix: true);
    Get.lazyPut(() => IventSquadController(state), tag: iventId, fenix: true);
  }
}
