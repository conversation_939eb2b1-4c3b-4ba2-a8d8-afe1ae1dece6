import 'package:get/get.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_bindings.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_collabrators.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_squad.dart';

class IventDetailPages {
  IventDetailPages._();

  static const _prefix = '/iventDetail';

  static const iventDetail = '$_prefix';
  static const whomYouJoin = '$_prefix/whomYouJoin';
  static const inviteMorePeople = '$_prefix/inviteMorePeople';
  static const participants = '$_prefix/participants';
  static const collaborators = '$_prefix/collaborators';
  static const squad = '$_prefix/squad';

  static final routes = [
    GetPage(
      name: iventDetail,
      page: () => IventDetail(iventId: Get.parameters['iventId']!),
      binding: IventDetailBindings(),
    ),
    GetPage(name: collaborators, page: () => IventDetailCollabrators(iventId: Get.parameters['iventId']!)),
    GetPage(name: squad, page: () => IventDetailSquad(iventId: Get.parameters['iventId']!)),
  ];
}
