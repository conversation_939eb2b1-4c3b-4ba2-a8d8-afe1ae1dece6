import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/content_tabs.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/profile_app_bar.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/user_info_widget.dart';
import 'package:ivent_app/features/side_menu/ia_profile_simple_drawer.dart';

class UserProfile extends GetView<ProfileUserInfoController> {
  final String userId;

  const UserProfile({Key? key, required this.userId}) : super(key: key);

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.basic(
      onRefresh: controller.refreshUserInfo,
      drawer: const IaProfileSimpleDrawer(), // Sadece profil sekmesinde drawer göster
      appBar: ProfileAppBar(userId: userId),
      body: DefaultTabController(
        length: 2,
        child: NestedScrollView(
          headerSliverBuilder: (context, value) => [
            SliverToBoxAdapter(child: UserInfoWidget(userId: userId)),
          ],
          body: ContentTabs(userId: userId),
        ),
      ),
    );
  }
}
