import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_friends_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class UserFriends extends GetView<ProfileFriendsController> {
  final String userId;

  const UserFriends({Key? key, required this.userId}) : super(key: key);

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Arkadaşlar',
      textEditingController: controller.textController,
      body: Obx(() {
        final friendsResult = controller.friendsResult;
        return IaSearchResultsBuilder(
          entityName: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: friendsResult!.friendCount,
            itemBuilder: (context, index) => _SearchResult(friend: friendsResult.friends[index]),
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends StatelessWidget {
  const _SearchResult({
    required this.friend,
  });

  final UserListItem friend;

  @override
  Widget build(BuildContext context) {
    return IaListTile.withImageUrl(
      avatarUrl: friend.avatarUrl,
      title: '@${friend.username}',
      subtitle: friend.university,
      onTap: () => Get.toNamed(ProfilePages.userProfile, parameters: {'id': friend.userId}, id: Get.find<AppNavigationController>().currentIndex),
      trailing: SharedButtons.moreVertical(),
    );
  }
}
