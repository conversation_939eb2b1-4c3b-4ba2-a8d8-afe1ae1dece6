import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileFollowersController extends BaseControllerWithSearch<ProfileSharedState> {
  final _followersResult = Rxn<GetFollowersByUserIdReturn>();

  ProfileFollowersController(ProfileSharedState state) : super(state);

  GetFollowersByUserIdReturn? get followersResult => _followersResult.value;

  @override
  bool get isResultsEmpty => followersResult?.followers.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _followersResult.value = await usersApi.getFollowersByUserId(
      state.userId,
      q: query,
    );
  }
}
