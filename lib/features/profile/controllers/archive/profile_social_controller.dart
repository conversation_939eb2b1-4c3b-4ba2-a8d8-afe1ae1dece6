import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileSocialController extends BaseController<ProfileSharedState> {
  ProfileSocialController(ProfileSharedState state) : super(state);

  final _hobbies = Rx<SearchHobbiesReturn?>(null);
  final _groups = RxList<GroupListItem>([]);
  final _groupDetail = Rx<GetGroupByGroupIdReturn?>(null);
  final _groupCreatedName = ''.obs;
  final _groupCreatedThumbnailUrl = ''.obs;
  final _groupCreatedUserIds = RxList<String>([]);

  SearchHobbiesReturn? get hobbies => _hobbies.value;
  set hobbies(SearchHobbiesReturn? value) => _hobbies.value = value;

  List<GroupListItem> get groups => _groups;
  set groups(List<GroupListItem> value) => _groups.assignAll(value);

  GetGroupByGroupIdReturn? get groupDetail => _groupDetail.value;
  set groupDetail(GetGroupByGroupIdReturn? value) => _groupDetail.value = value;

  String get groupCreatedName => _groupCreatedName.value;
  set groupCreatedName(String value) => _groupCreatedName.value = value;

  String get groupCreatedThumbnailUrl => _groupCreatedThumbnailUrl.value;
  set groupCreatedThumbnailUrl(String value) => _groupCreatedThumbnailUrl.value = value;

  List<String> get groupCreatedUserIds => _groupCreatedUserIds;
  set groupCreatedUserIds(List<String> value) => _groupCreatedUserIds.assignAll(value);
}
