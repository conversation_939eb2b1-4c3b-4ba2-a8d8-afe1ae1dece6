import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileJoinedIventsController extends BaseControllerWithSearch<ProfileSharedState> {
  final _iventsResult = Rxn<GetIventsByUserIdReturn>();

  ProfileJoinedIventsController(ProfileSharedState state) : super(state);

  GetIventsByUserIdReturn? get iventsResult => _iventsResult.value;

  @override
  bool get isResultsEmpty => iventsResult?.ivents.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _iventsResult.value = await usersApi.getIventsByUserId(
      state.userId,
      IventListingTypeEnum.joined,
      q: query,
    );
  }
}
