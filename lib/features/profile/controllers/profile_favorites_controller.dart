import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/shared/controllers/ivent_favorite_controller.dart';

class ProfileFavoritesController extends BaseControllerWithSearch<ProfileSharedState> {
  final _favoritesResult = Rxn<GetFavoritesByUserIdReturn>();

  late final IventFavoriteController favoriteController;

  ProfileFavoritesController(ProfileSharedState state) : super(state) {
    final localGetter = (iventId) => favoritesResult!.ivents.any((e) => e.iventId == iventId && e.isFavorited);
    favoriteController = IventFavoriteController(this, localGetter);
  }

  GetFavoritesByUserIdReturn? get favoritesResult => _favoritesResult.value;

  @override
  bool get isResultsEmpty => favoritesResult?.ivents.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _favoritesResult.value = await usersApi.getFavoritesByUserId(
      state.userId,
      q: query,
    );
  }

  bool isIventFavorited(String iventId) => favoriteController.isFavorited(iventId);
  void toggleFavorite(String iventId) async => await favoriteController.toggleFavorite(iventId);
}
