import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/ia_app_bar.dart';
import 'package:ivent_app/core/widgets/ia_builder.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/routes/settings.dart';

class ProfileAppBar extends GetView<ProfileUserInfoController> {
  final String userId;

  const ProfileAppBar({super.key, required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Obx(() => IaBuilder(
        isLoading: controller.isLoading('loadUserInfo'),
        isEmpty: controller.userPageInfo == null,
        builder: (context) => IaAppBar.centered(
              title: controller.userPageInfo!.fullname,
              subtitle: controller.userPageInfo!.username,
              leading: _Leading(userId: userId),
              trailing: _Trailing(userId: userId),
            )));
  }
}

// Leading button behavior:
// 1. First Person (user's own profile):
//    - Shows hamburger menu icon
//    - Opens the drawer when tapped
// 2. Third Person (other user's profile):
//    - Shows back arrow (chevron left) icon
//    - Returns to previous screen when tapped
class _Leading extends GetView<ProfileUserInfoController> {
  final String userId;

  const _Leading({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: controller.userPageInfo!.isFirstPerson
          ? () {
              debugPrint('ProfileAppBar: Opening drawer');
              Scaffold.of(context).openDrawer();
            }
          : () => Get.back(),
      iconPath: controller.userPageInfo!.isFirstPerson
          ? AppAssets.hamburgerMD
          : AppAssets.chevronLeft,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}

// Trailing button behavior:
// 1. First Person (user's own profile):
//    - Shows settings icon
//    - Opens the settings page when tapped
// 2. Third Person (other user's profile):
//    - Shows more vertical (3 dots) icon
//    - Opens block/report options when tapped
class _Trailing extends GetView<ProfileUserInfoController> {
  final String userId;

  const _Trailing({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    if (controller.userPageInfo!.isFirstPerson) {
      return IaIconButton(
        onPressed: () => Get.toNamed(SettingsPages.settings, id: Get.find<AppNavigationController>().currentIndex),
        iconPath: AppAssets.settings,
        iconSize: AppDimensions.defaultIconButtonSize,
        iconColor: AppColors.darkGrey,
      );
    } else {
      return IaIconButton(
        onPressed: controller.openBlockSettings,
        iconPath: AppAssets.moreVertical,
        iconSize: AppDimensions.defaultIconButtonSize,
        iconColor: AppColors.darkGrey,
      );
    }
  }
}
