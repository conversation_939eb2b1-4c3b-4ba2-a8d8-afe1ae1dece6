import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/create_vibe_button.dart';
import 'package:ivent_app/routes/vibes.dart';

class IaVibeThumbnail extends StatelessWidget {
  final String? vibeId;
  final String? iventId;
  final String iventName;
  final int participantCount;
  final List<String> participantNames;
  final String? imageUrl;
  final List<DateTime>? date;

  const IaVibeThumbnail({
    super.key,
    this.vibeId,
    this.iventId,
    required this.iventName,
    required this.participantCount,
    required this.participantNames,
    this.imageUrl,
    this.date,
  }) : assert((vibeId != null && imageUrl != null) || (date != null && iventId != null));

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _Thumbnail(imageUrl: imageUrl),
        _Overlay(
          vibeId: vibeId,
          date: date,
          iventName: iventName,
          participantCount: participantCount,
          participantNames: participantNames,
        ),
      ],
    );
  }
}

class _Overlay extends StatelessWidget {
  const _Overlay({
    required this.vibeId,
    required this.date,
    required this.iventName,
    required this.participantCount,
    required this.participantNames,
  });

  final String? vibeId;
  final List<DateTime>? date;
  final String iventName;
  final int participantCount;
  final List<String> participantNames;

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      onTap: () {
        if (vibeId == null) return;
        Get.toNamed(VibesPages.singleVibePage, parameters: {'vibeId': vibeId!}, id: Get.find<AppNavigationController>().currentIndex);
      },
      padding: const EdgeInsets.all(AppDimensions.padding16),
      roundness: AppDimensions.radiusXL,
      gradient: date == null ? AppColors.gradientBlackL : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _Title(iventName: iventName, date: date),
          _ParticipantsText(participantCount: participantCount, participantNames: participantNames),
          if (date != null) const Spacer(),
          if (date != null) _DateRow(date: date),
        ],
      ),
    );
  }
}

class _Title extends StatelessWidget {
  const _Title({
    required this.iventName,
    required this.date,
  });

  final String iventName;
  final List<DateTime>? date;

  @override
  Widget build(BuildContext context) {
    return Text(
      iventName,
      style: AppTextStyles.size20Bold.copyWith(color: date == null ? Colors.white : AppColors.textPrimary),
      maxLines: 1,
      overflow: TextOverflow.fade,
      softWrap: false,
    );
  }
}

class _ParticipantsText extends StatelessWidget {
  const _ParticipantsText({
    required this.participantCount,
    required this.participantNames,
  });

  final int participantCount;
  final List<String> participantNames;

  @override
  Widget build(BuildContext context) {
    var text;
    if (participantCount == 0)
      text = '';
    else if (participantCount == 1)
      text = participantNames[0];
    else if (participantCount == 2)
      text = '${participantNames[0]}, ${participantNames[1]}';
    else
      text = '${participantNames[0]}, ${participantNames[1]} ve ${participantCount - 2} diğer';

    return Text(
      text,
      style: AppTextStyles.size14Medium.copyWith(color: AppColors.textTertiary),
      maxLines: 1,
      overflow: TextOverflow.fade,
      softWrap: false,
    );
  }
}

class _DateRow extends StatelessWidget {
  const _DateRow({
    required this.date,
  });

  final List<DateTime>? date;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            DateFormat('d MMMM yyyy, HH:mm').format(date![0]),
            style: AppTextStyles.size12MediumTextSecondary,
            maxLines: 2,
            softWrap: false,
          ),
        ),
        const SizedBox(width: AppDimensions.padding8),
        const ProfileCreateVibe(),
      ],
    );
  }
}

class _Thumbnail extends StatelessWidget {
  const _Thumbnail({
    required this.imageUrl,
  });

  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    if (imageUrl != null) {
      return IaImageContainer.withImageUrl(
        imageUrl: imageUrl!,
        roundness: AppDimensions.radiusXL,
        aspectRatio: 0.6,
      );
    }
    return IaImageContainer.withChild(
      child: Container(),
      roundness: AppDimensions.radiusXL,
      borderColor: AppColors.mediumGrey,
      borderWidth: 2,
      aspectRatio: 0.6,
    );
  }
}
