import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class ProfileCounters extends StatelessWidget {
  final int count;
  final String text;
  final VoidCallback? onTap;

  const ProfileCounters._({
    required this.count,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Text(count.toString(), style: AppTextStyles.size16Bold),
          Text(text, style: AppTextStyles.size12MediumTextSecondary),
        ],
      ),
    );
  }

  /// Factory constructor for ivent count display
  static ProfileCounters followingCounter(GetUserByUserIdReturn userPageInfo) {
    final id = Get.find<AppNavigationController>().currentIndex;
    return ProfileCounters._(
      count: userPageInfo.followingCount,
      text: 'Takip Edilenler',
      onTap: () => Get.toNamed(ProfilePages.userFollowings, id: id, parameters: {'id': userPageInfo.userId}),
    );
  }

  /// Factory constructor for friend count display
  static ProfileCounters friendCounter(GetUserByUserIdReturn userPageInfo) {
    final id = Get.find<AppNavigationController>().currentIndex;
    return ProfileCounters._(
      count: userPageInfo.friendCount,
      text: 'Arkadaşlar',
      onTap: () => Get.toNamed(ProfilePages.userFriends, id: id, parameters: {'id': userPageInfo.userId}),
    );
  }

  /// Factory constructor for follower count display
  static ProfileCounters followerCounter(GetUserByUserIdReturn userPageInfo) {
    final id = Get.find<AppNavigationController>().currentIndex;
    return ProfileCounters._(
      count: userPageInfo.followerCount,
      text: 'Takipçiler',
      onTap: () => Get.toNamed(ProfilePages.userFollowers, id: id, parameters: {'id': userPageInfo.userId}),
    );
  }
}
