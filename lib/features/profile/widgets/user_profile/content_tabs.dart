import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/features/profile/controllers/profile_favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';

class ContentTabs extends GetView<ProfileUserInfoController> {
  final String userId;

  const ContentTabs({super.key, required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const _TabControls(),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: AppDimensions.padding20, bottom: 100),
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              children: [_Favorites(userId: userId), _Diary(userId: userId)],
            ),
          ),
        ),
      ],
    );
  }
}

class _TabControls extends StatelessWidget {
  const _TabControls();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        height: 40,
        child: TabBar(
          dividerHeight: 0,
          indicatorColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          labelStyle: AppTextStyles.size14Bold,
          unselectedLabelStyle: AppTextStyles.size14BoldTextSecondary,
          tabs: const [Tab(text: 'Favori Listen'), Tab(text: 'Günlük')],
        ),
      ),
    );
  }
}

class _Favorites extends GetView<ProfileFavoritesController> {
  final String userId;

  const _Favorites({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaSearchResultsBuilder(
        entityName: 'iVent',
        isSearching: controller.isSearching,
        isResultsEmpty: controller.isResultsEmpty,
        isQueryEmpty: controller.isQueryEmpty,
        initialSearchBehavior: InitialSearchBehavior.loaded,
        builder: (context) => _FavoriteList(userId: userId),
      );
    });
  }
}

class _FavoriteList extends GetView<ProfileFavoritesController> {
  final String userId;

  const _FavoriteList({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: controller.favoritesResult!.iventCount,
      itemBuilder: (context, index) {
        final ivent = controller.favoritesResult!.ivents[index];
        return _FavoriteSearchResult(controller: controller, ivent: ivent);
      },
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }
}

class _FavoriteSearchResult extends StatelessWidget {
  const _FavoriteSearchResult({
    required this.controller,
    required this.ivent,
  });

  final ProfileFavoritesController controller;
  final IventListItemWithIsFavorited ivent;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isFavorited = controller.isIventFavorited(ivent.iventId);
      return IaIventTile(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding16),
        imageUrl: ivent.thumbnailUrl,
        iventName: ivent.iventName,
        locationName: ivent.locationName,
        date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
        memberAvatarUrls: ivent.memberAvatarUrls,
        memberCount: ivent.memberCount,
        memberNames: ivent.memberFirstnames,
        isOrganizerUser: ivent.creatorType != IventCreatorTypeEnum.distributor,
        organizerName: ivent.creatorUsername,
        organizerAvatarUrl: ivent.creatorImageUrl,
        viewType: ivent.viewType,
        onTap: () => Get.toNamed(IventDetailPages.iventDetail,
            id: Get.find<AppNavigationController>().currentIndex, parameters: {'iventId': ivent.iventId}),
        isFavorited: isFavorited,
        onFavorite: () => controller.toggleFavorite(ivent.iventId),
      );
    });
  }
}

// class _FavoriteGrid extends GetView<ProfileFavoritesController> {
//   final String userId;

//   const _FavoriteGrid({required this.userId});

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() => IventGrid(
//           iventItems: controller.favoritesResult!.ivents,
//           onFavorite: onFavorite,
//           getIsFavorite: getIsFavorite,
//         ));
//   }
// }

class _Diary extends GetView<ProfileJoinedIventsController> {
  final String userId;

  const _Diary({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final joinedIventsResult = controller.iventsResult;
      return IaSearchResultsBuilder(
        entityName: 'iVent',
        isSearching: controller.isSearching,
        isResultsEmpty: controller.isResultsEmpty,
        isQueryEmpty: controller.isQueryEmpty,
        initialSearchBehavior: InitialSearchBehavior.loaded,
        builder: (context) => ListView.separated(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: joinedIventsResult!.iventCount,
          itemBuilder: (context, index) {
            final ivent = joinedIventsResult.ivents[index];
            return _IventSearchResult(ivent: ivent);
          },
          separatorBuilder: IaListTile.separatorBuilder20,
        ),
      );
    });
  }
}

class _IventSearchResult extends StatelessWidget {
  const _IventSearchResult({
    required this.ivent,
  });

  final IventListItem ivent;

  @override
  Widget build(BuildContext context) {
    return IaIventTile(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding16),
      imageUrl: ivent.thumbnailUrl,
      iventName: ivent.iventName,
      locationName: ivent.locationName,
      date: ivent.dates.map((e) => DateTime.parse(e)).toList(),
      memberAvatarUrls: ivent.memberAvatarUrls,
      memberCount: ivent.memberCount,
      memberNames: ivent.memberFirstnames,
      isOrganizerUser: ivent.creatorType != IventCreatorTypeEnum.distributor,
      organizerName: ivent.creatorUsername,
      organizerAvatarUrl: ivent.creatorImageUrl,
      viewType: ivent.viewType,
      onTap: () => Get.toNamed(IventDetailPages.iventDetail,
          id: Get.find<AppNavigationController>().currentIndex, parameters: {'iventId': ivent.iventId}),
    );
  }
}
