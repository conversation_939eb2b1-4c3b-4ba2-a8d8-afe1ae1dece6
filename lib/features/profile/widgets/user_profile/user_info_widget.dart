import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_circle_avatar.dart';
import 'package:ivent_app/core/widgets/ia_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/archive/profile_buttons.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/profile_counters.dart';

class UserInfoWidget extends GetView<ProfileUserInfoController> {
  final String userId;

  const UserInfoWidget({super.key, required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Obx(() => IaBuilder(
          isLoading: controller.isLoading('loadUserInfo'),
          isEmpty: controller.userPageInfo == null,
          builder: (context) => Column(
            children: [
              _InfoHeader(userId: userId),
              controller.userPageInfo!.isFirstPerson ? const SizedBox.shrink() : _InfoButtons(userId: userId),
              _InfoHobbies(userId: userId),
            ],
          ),
        ));
  }
}

class _InfoHeader extends GetView<ProfileUserInfoController> {
  final String userId;

  const _InfoHeader({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(child: Center(child: ProfileCounters.followingCounter(controller.userPageInfo!))),
          IaCircleAvatar(imageUrl: controller.userPageInfo!.avatarUrl, radius: 32, iconPath: AppAssets.user01),
          Expanded(
              child: Center(
                  child: controller.userPageInfo!.userRole == UserRoleEnum.creator
                      ? ProfileCounters.followerCounter(controller.userPageInfo!)
                      : ProfileCounters.friendCounter(controller.userPageInfo!))),
        ],
      ),
    );
  }
}

class _InfoButtons extends GetView<ProfileUserInfoController> {
  final String userId;

  const _InfoButtons({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        spacing: AppDimensions.padding12,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: _buildButtonLayout().map((buttonLayout) => Expanded(child: buttonLayout)).toList(),
      ),
    );
  }

  List<Widget> _buildButtonLayout() {
    final userPageInfo = controller.userPageInfo!;
    final isCreator = userPageInfo.userRole == UserRoleEnum.creator;

    if (isCreator) {
      return [
        Obx(() => ProfileButtons.profileFollow(onTap: controller.toggleFollowing, isFollowing: controller.isFollowing)),
        Obx(() => ProfileButtons.profileAddFriend(
            onTap: controller.toggleFriendship, relationshipStatus: controller.relationshipStatus)),
      ];
    } else {
      return [
        Obx(() => ProfileButtons.profileAddFriend(
            onTap: controller.toggleFriendship, relationshipStatus: controller.relationshipStatus)),
      ];
    }
  }
}

class _InfoHobbies extends GetView<ProfileUserInfoController> {
  final String userId;

  const _InfoHobbies({required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Row(
        children: [
          if (controller.userPageInfo!.isFirstPerson)
            const Padding(
              padding: EdgeInsets.only(left: AppDimensions.padding20, right: AppDimensions.padding8),
              child: _EditHobbyButton(),
            ),
          Expanded(
            child: SizedBox(
              height: AppDimensions.buttonHeightProfileTag,
              child: ListView.separated(
                padding: const EdgeInsets.only(right: AppDimensions.padding20),
                scrollDirection: Axis.horizontal,
                itemCount: controller.userPageInfo!.hobbies.length,
                itemBuilder: (context, index) => _HobbyTag(
                  onTap: () {},
                  text: controller.userPageInfo!.hobbies[index],
                ),
                separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _EditHobbyButton extends StatelessWidget {
  const _EditHobbyButton();

  @override
  Widget build(BuildContext context) {
    return IaCircularButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeProfileTagEdit,
      backgroundColor: AppColors.primary,
      iconPath: AppAssets.editPencil02,
      iconSize: AppDimensions.buttonSizeProfileTagEdit,
    );
  }
}

class _HobbyTag extends StatelessWidget {
  final VoidCallback? onTap;
  final String text;

  const _HobbyTag({
    this.onTap,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightProfileTag,
      roundness: AppDimensions.buttonRadiusL,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.size12Medium,
    );
  }
}
