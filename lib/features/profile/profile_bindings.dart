import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';

class ProfileBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final userId = Get.parameters['id']!;
    final isOwnProfile = userId == service.sessionUser!.sessionId;
    final state = Get.put(ProfileSharedState(userId), tag: userId, permanent: isOwnProfile);

    Get.lazyPut(() => ProfileUserInfoController(state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileJoinedIventsController(state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFriendsController(state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFollowingsController(state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFavoritesController(state), tag: userId, fenix: true);
  }
}
