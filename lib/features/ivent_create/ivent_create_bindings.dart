import 'package:get/get.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_date_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_submission_controller.dart';

class IventCreateBindings implements Bindings {
  @override
  void dependencies() {
    final state = Get.put(IventCreateSharedState());

    Get.lazyPut<IventCreateFormController>(() => IventCreateFormController(state));
    Get.lazyPut<IventCreateImageController>(() => IventCreateImageController(state));
    Get.lazyPut<IventCreateMapController>(() => IventCreateMapController(state));
    Get.lazyPut<IventCreateDateController>(() => IventCreateDateController(state));
    Get.lazyPut<IventCreateSubmissionController>(() => IventCreateSubmissionController(state));
  }
}
