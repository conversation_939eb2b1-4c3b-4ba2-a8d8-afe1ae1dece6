import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';

class IventCreateDateController extends BaseController<IventCreateSharedState> {
  IventCreateDateController(IventCreateSharedState state) : super(state);

  final selectedDate = Rxn<DateTime>();

  List<DateTime> get dates => state.dates;
  bool get canContinue => dates.isNotEmpty;

  void handleDateSelected(DateTime dateTime) => selectedDate.value = dateTime;

  void handleDateAccepted() {
    if (selectedDate.value == null) return;
    state.dates.add(selectedDate.value!);
    selectedDate.value = null;
    Get.back();
  }

  void handleDateRemoved(int index) {
    final dates = List<DateTime>.from(state.dates);
    dates.removeAt(index);
    state.dates.value = dates;
  }
}
