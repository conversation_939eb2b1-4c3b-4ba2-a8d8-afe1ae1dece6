import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';
import 'package:ivent_app/features/side_menu/controllers/edu_verification_controller.dart';
import 'package:ivent_app/features/side_menu/utils/profile_dialogs.dart';

class EduVerificationControllerHelper {
  static EduVerificationController? initializeController() {
    try {
      final authService = Get.find<AuthService>();
      final profileState = Get.find<ProfileSharedState>();
      final controller = EduVerificationController(profileState);
      Get.put(controller);
      return controller;
    } catch (e) {
      _handleInitializationError();
      return null;
    }
  }

  static void _handleInitializationError() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.back();
      ProfileDialogs.showErrorDialog(
        'Hata',
        ProfileConstants.sessionRequiredText,
      );
    });
  }

  static void disposeController() {
    try {
      Get.delete<EduVerificationController>();
    } catch (e) {
      debugPrint('Error disposing EduVerificationController: $e');
    }
  }
}
