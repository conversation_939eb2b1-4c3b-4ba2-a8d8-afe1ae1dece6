import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/side_menu/constants/drawer_constants.dart';
import 'package:ivent_app/features/side_menu/utils/profile_drawer_dialogs.dart';
import 'package:ivent_app/features/side_menu/widgets/common/profile_drawer_header.dart';

class IaProfileSimpleDrawer extends GetView<AppNavigationController> {
  const IaProfileSimpleDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return controller.isDrawerVisible
          ? Drawer(
              backgroundColor: AppColors.background,
              child: Safe<PERSON>rea(
                child: Column(
                  children: [
                    const ProfileDrawerHeader(),
                    // TODO: These features will be added later. We decided this at the meeting held on 01/09/2025.
                    // const ProfileDrawerLevelInfo(),
                    // const SizedBox(height: AppDimensions.padding16),
                    // const ProfileDrawerActionButtons(),
                    // const SizedBox(height: AppDimensions.padding20),
                    // const ProfileDrawerPagesSection(),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.padding20),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: AppColors.lightGrey, width: 1),
                        ),
                      ),
                      child: Column(
                        children: [
                          _buildDrawerItem(
                            icon: AppAssets.closeCircle,
                            title: DrawerConstants.deleteAccountText,
                            titleColor: AppColors.error,
                            iconColor: AppColors.error,
                            onTap: () {
                              ProfileDrawerDialogs.showDeleteAccountDialog(context);
                            },
                          ),

                          // The logout item moved to the settings page.
                          // This change was made on branch: feat/settings-mvp
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          : const SizedBox.shrink();
    });
  }

  Widget _buildDrawerItem({
    required String icon,
    required String title,
    required VoidCallback onTap,
    Color? titleColor,
    Color? iconColor,
  }) {
    return ListTile(
      leading: IaSvgIcon(
        iconPath: icon,
        iconSize: 24,
        iconColor: iconColor ?? AppColors.darkGrey,
      ),
      title: Text(
        title,
        style: AppTextStyles.size14Medium.copyWith(
          color: titleColor ?? AppColors.textPrimary,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding20,
        vertical: AppDimensions.padding4,
      ),
    );
  }
}
