import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class LevelStep {
  final String id;
  final String title;
  final String description;
  final bool isCompleted;
  final bool isActionable;
  final String actionText;
  final VoidCallback? onAction;

  LevelStep({
    required this.id,
    required this.title,
    required this.description,
    required this.isCompleted,
    required this.isActionable,
    required this.actionText,
    this.onAction,
  });
}

class ProfileLevelStepsController extends BaseController<ProfileSharedState> {
  // State
  final _steps = <LevelStep>[].obs;
  final _isLoading = true.obs;
  final _currentUserLevel = 0.obs;
  final _hasError = false.obs;
  final _errorMessage = ''.obs;

  // Constructor
  ProfileLevelStepsController(ProfileSharedState state) : super(state);

  // Getters
  List<LevelStep> get steps => _steps;
  int get currentUserLevel => _currentUserLevel.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;

  String get levelTitle {
    if (currentUserLevel == 5) return 'Tam Hesap (5/5)';
    if (currentUserLevel >= 1 && currentUserLevel <= 4) return 'Bilinçli Hesap ($currentUserLevel/4)';
    return 'Bilinçli Hesap (0/1)';
  }

  String get levelDescription {
    if (currentUserLevel == 5) return 'Tebrikler! Tüm adımları tamamladınız.';
    if (currentUserLevel >= 1) return 'Bilinçli hesap seviyenizi artırmak için aşağıdaki adımları tamamlayın.';
    return 'Hesabınızı geliştirmek için aşağıdaki adımları tamamlayın.';
  }

  bool get isAllStepsCompleted => steps.every((step) => step.isCompleted);
  bool get canProceedToNextStep => steps.where((step) => !step.isCompleted).isNotEmpty;

  String get nextStepButtonText {
    if (isAllStepsCompleted) return 'Tamamlandı';
    final nextStep = steps.firstWhere((step) => !step.isCompleted, orElse: () => steps.first);
    return 'Devam Et: ${nextStep.title}';
  }

  // Lifecycle
  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await _loadUserLevel();
    await _loadLevelSteps();
    _isLoading.value = false;
    debugPrint('ProfileLevelStepsController has been initialized with user: ${sessionUser.sessionId}');
  }

  // Public methods
  void onStepAction(LevelStep step) {
    step.onAction?.call();
  }

  void onNextStep() {
    if (!isAllStepsCompleted) {
      final nextStep = steps.firstWhere((step) => !step.isCompleted, orElse: () => steps.first);
      onStepAction(nextStep);
    }
  }

  // Common methods (following the pattern from other controllers)

  /// Handles common error scenarios for level steps operations

  /// Clears any error state
  void clearError() {
    _hasError.value = false;
    _errorMessage.value = '';
  }

  // Private methods
  Future<void> _loadUserLevel() async {
    try {
      final userRole = sessionUser.sessionRole;
      switch (userRole) {
        case UserRoleEnum.creator:
          _currentUserLevel.value = 5;
          break;
        case UserRoleEnum.level5:
          _currentUserLevel.value = 5;
          break;
        case UserRoleEnum.level4:
          _currentUserLevel.value = 4;
          break;
        case UserRoleEnum.level3:
          _currentUserLevel.value = 3;
          break;
        case UserRoleEnum.level2:
          _currentUserLevel.value = 2;
          break;
        case UserRoleEnum.level1:
          _currentUserLevel.value = 1;
          break;
        case UserRoleEnum.level0:
        default:
          _currentUserLevel.value = 0;
          break;
      }
    } catch (e) {
      handleError(e);
      _currentUserLevel.value = 0;
    }
  }

  Future<void> _loadLevelSteps() async {
    try {
      // Backend'den level bilgilerini al
      final levelData = await usersApi.getLevelByUserId(sessionUser.sessionId);

      // Adımları oluştur
      _steps.assignAll(_createStepsBasedOnLevel(levelData));
    } catch (e) {
      debugPrint('ProfileLevelSteps: Failed to load level data: $e');
      // API başarısız olursa kullanıcı seviyesine göre temel adımları oluştur
      _steps.assignAll(_createStepsBasedOnLevel(null));
      // Kullanıcıya bilgi ver
      Get.snackbar(
        'Bilgi',
        'Level bilgileri yüklenirken bir sorun oluştu. Temel adımlar gösteriliyor.',
        duration: const Duration(seconds: 3),
      );
    }
  }

  List<LevelStep> _createStepsBasedOnLevel(GetLevelByUserIdReturn? levelData) {
    // Backend'den gelen veriye göre adımları oluştur
    final steps = <LevelStep>[];

    // Step 1: Profil Bilgilerini Tamamla
    steps.add(LevelStep(
      id: 'profile_complete',
      title: 'Profil Bilgilerini Tamamla',
      description: 'Profil fotoğrafı, bio ve kişisel bilgilerinizi ekleyin',
      isCompleted: currentUserLevel >= 1,
      isActionable: true,
      actionText: 'Tamamla',
      onAction: _goToProfileEdit,
    ));

    if (currentUserLevel >= 1) {
      // Step 2: Favorilere 5 iVent Ekle
      steps.add(LevelStep(
        id: 'add_favorites',
        title: 'Favorilere 5 iVent Ekle',
        description: 'İlginizi çeken etkinlikleri favorilerinize ekleyin',
        isCompleted: currentUserLevel >= 2,
        isActionable: true,
        actionText: 'Ekle',
        onAction: _goToFavorites,
      ));
    }

    if (currentUserLevel >= 2) {
      // Step 3: En az 2 Kişilik bir Memories Oluştur
      steps.add(LevelStep(
        id: 'create_memories',
        title: 'En az 2 Kişilik bir Memories Oluştur',
        description: 'Arkadaşlarınızla anılar oluşturun',
        isCompleted: currentUserLevel >= 3,
        isActionable: true,
        actionText: 'Oluştur',
        onAction: _goToMemories,
      ));
    }

    if (currentUserLevel >= 3) {
      // Step 4: Bir Arkadaş Grubu Oluştur
      steps.add(LevelStep(
        id: 'create_group',
        title: 'Bir Arkadaş Grubu Oluştur',
        description: 'Arkadaşlarınızla grup oluşturun',
        isCompleted: currentUserLevel >= 4,
        isActionable: true,
        actionText: 'Oluştur',
        onAction: _goToCreateGroup,
      ));
    }

    if (currentUserLevel >= 4) {
      // Step 5: Tam Hesap
      steps.add(LevelStep(
        id: 'full_account',
        title: 'Tam Hesap',
        description: 'Tüm özelliklerden yararlanın',
        isCompleted: currentUserLevel >= 5,
        isActionable: false,
        actionText: '',
      ));
    }

    return steps;
  }

  // Action methods (following navigation patterns from other controllers)
  void _goToProfileEdit() {
    try {
      // Navigate to profile page for editing
      Get.toNamed('/profile_page', parameters: {'id': sessionUser.sessionId});
    } catch (e) {
      handleError(e);
    }
  }

  void _goToFavorites() {
    try {
      // Navigate to favorites page using ProfileRoutes
      Get.toNamed('/profile_page_favoriler', parameters: {'id': sessionUser.sessionId});
    } catch (e) {
      handleError(e);
    }
  }

  void _goToMemories() {
    try {
      // Navigate to memories creation - this would need to be implemented
      Get.snackbar('Bilgi', 'Memories oluşturma özelliği yakında gelecek...');
    } catch (e) {
      handleError(e);
    }
  }

  void _goToCreateGroup() {
    try {
      // Navigate to group creation - this would need to be implemented
      Get.snackbar('Bilgi', 'Grup oluşturma özelliği yakında gelecek...');
    } catch (e) {
      handleError(e);
    }
  }
}
