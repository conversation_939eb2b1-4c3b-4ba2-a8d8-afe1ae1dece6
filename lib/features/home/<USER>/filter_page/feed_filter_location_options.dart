import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_strings.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/home/<USER>/filter_page/buttons/feed_filter_location_button.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/feed_filter_location_slider.dart';

class FeedFilterLocationOptions extends GetView<HomeFilterController> {
  const FeedFilterLocationOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() => FeedFilterLocationButton(
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              onTap: () => Get.toNamed(HomePages.locationPage, id: Get.find<AppNavigationController>().currentIndex),
              text: controller.state.selectedPlace.value != null ? controller.state.selectedPlace.value!.name : 'Yok',
            )),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(HomeStrings.iventAkisSenin, style: AppTextStyles.size14Regular),
              const SizedBox(height: AppDimensions.padding32),
              Obx(() => FeedFilterLocationSlider(
                    value: controller.state.locationCoeff.toDouble(),
                    onChanged: (newValue) => controller.state.locationCoeff.value = newValue.toInt(),
                    label: controller.state.locationCoeff.toStringAsFixed(0),
                  )),
            ],
          ),
        ),
      ],
    );
  }
}
