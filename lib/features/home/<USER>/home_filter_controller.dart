import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeFilterController extends BaseController<HomeSharedState> {
  HomeFilterController(HomeSharedState state) : super(state);

  void toggleHobbySelection(String hobbyId) {
    state.toggleHobbySelection(hobbyId);
  }

  void clearFilters() {
    state.selectedHobbyIds.value = [];
  }

  void updateLocationCoeff(int value) {
    state.locationCoeff.value = value;
  }

  void applyFilters() {
    Get.back();
  }
}
