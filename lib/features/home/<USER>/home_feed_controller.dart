import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/feed_filters.dart';
import 'package:ivent_app/shared/controllers/ivent_favorite_controller.dart';

class HomeFeedController extends BaseController<HomeSharedState> {
  final _iventItems = <IventCardItem>[].obs;
  final _existingFeedFilters = Rxn<FeedFilters>();
  final _feedPage = 1.obs;
  final _feedContinuable = true.obs;

  late final IventFavoriteController favoriteController;

  HomeFeedController(HomeSharedState state) : super(state) {
    final localGetter = (iventId) => iventItems.any((e) => e.iventId == iventId && e.isFavorited);
    favoriteController = IventFavoriteController(this, localGetter);
  }

  List<IventCardItem> get iventItems => _iventItems.toList();
  FeedFilters? get existingFeedFilters => _existingFeedFilters.value;
  int get feedPage => _feedPage.value;
  bool get feedContinuable => _feedContinuable.value;

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await retrieveIventList();
  }

  Future<void> retrieveIventList() async {
    await runSafe(tag: 'retrieveIventList', () async {
      _feedContinuable.value = true;
      final params = _buildFeedParams();
      _existingFeedFilters.value = params;

      final response = await homeApi.feed(
        params.dateType,
        params.categories,
        locationCoeff: params.locationCoeff,
        q: params.q,
        page: 1,
        latitude: params.latitude,
        longitude: params.longitude,
        endDate: params.endDate,
        startDate: params.startDate,
      );

      if (response != null && response.ivents.isNotEmpty) {
        _iventItems.value = response.ivents;
      }
    });
  }

  Future<void> loadMoreIventItems() async {
    await runSafe(
      () async {
        if (!feedContinuable) return;
        _feedPage.value++;

        final response = await homeApi.feed(
          _existingFeedFilters.value!.dateType,
          _existingFeedFilters.value!.categories,
          locationCoeff: _existingFeedFilters.value!.locationCoeff,
          q: _existingFeedFilters.value!.q,
          page: _feedPage.value,
          latitude: _existingFeedFilters.value!.latitude,
          longitude: _existingFeedFilters.value!.longitude,
          endDate: _existingFeedFilters.value!.endDate,
          startDate: _existingFeedFilters.value!.startDate,
        );

        if (response != null && response.ivents.isNotEmpty) {
          _iventItems.value = [...iventItems, ...response.ivents];
        } else {
          _feedContinuable.value = false;
        }
      },
      tag: 'loadMoreIventItems',
      onError: () => _feedPage.value--,
    );
  }

  void toggleTimeFilter(int index) {
    state.dateFilterIndex.value = index;

    if (index != 0) state.selectedDates.value = [];

    _feedPage.value = 1;
    retrieveIventList();
  }

  void setSelectedDates(DateTime startDate, DateTime endDate) {
    state.selectedDates.value = [startDate, endDate];

    state.dateFilterIndex.value = FeedDateEnum.values.indexOf(FeedDateEnum.range);

    _feedPage.value = 1;
    retrieveIventList();
  }

  String selectedDatesAsString() {
    if (state.selectedDates.isEmpty) return 'Başlangıç Tarihi Seçiniz';
    if (state.selectedDates.length == 1) return 'Bitiş Tarihi Seçiniz';
    final format = DateFormat('d MMM');
    final startDate = format.format(state.selectedDates[0]);
    final endDate = format.format(state.selectedDates[1]);
    return '$startDate - $endDate';
  }

  void applyFilters() {
    _feedPage.value = 1;
    retrieveIventList();
  }

  FeedFilters _buildFeedParams() {
    final params = FeedFilters(
      dateType: FeedDateEnum.values[state.dateFilterIndex.value],
      categories: '',
      locationCoeff: state.locationCoeff.value,
      q: '',
    );

    if (state.selectedHobbyIds.isNotEmpty) {
      params.categories = state.selectedHobbyIds.join(',');
    }

    if (state.selectedPlace.value != null) {
      params.latitude = state.selectedPlace.value!.latitude;
      params.longitude = state.selectedPlace.value!.longitude;
    }

    if (state.selectedDates.length == 2) {
      params.startDate = _formatDateForApi(state.selectedDates[0]);
      params.endDate = _formatDateForApi(state.selectedDates[1]);
    }

    return params;
  }

  String _formatDateForApi(DateTime date) => DateFormat('yyyy-MM-dd').format(date);

  bool isIventFavorited(String iventId) => favoriteController.isFavorited(iventId);
  void toggleFavorite(String iventId) async => await favoriteController.toggleFavorite(iventId);
}
