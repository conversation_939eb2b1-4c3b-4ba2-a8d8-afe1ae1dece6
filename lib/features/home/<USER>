import 'package:get/get.dart';
import 'package:ivent_app/features/home/<USER>/home_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_ivents_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_location_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_search_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeBindings implements Bindings {
  @override
  void dependencies() {
    final state = Get.put(HomeSharedState(), permanent: true);

    Get.lazyPut(() => HomeAccountsController(state), fenix: true);
    Get.lazyPut(() => HomeFeedController(state), fenix: true);
    Get.lazyPut(() => HomeFilterController(state), fenix: true);
    Get.lazyPut(() => HomeIventsController(state), fenix: true);
    Get.lazyPut(() => HomeSearchController(state), fenix: true);
    Get.lazyPut(() => HomePanelsController(state), fenix: true);
    Get.lazyPut(() => HomeMapController(state), fenix: true);
    Get.lazyPut(() => HomeLocationController(state), fenix: true);
  }
}
