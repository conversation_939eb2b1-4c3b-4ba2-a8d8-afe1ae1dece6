import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeAccountsController extends BaseControllerWithSearch<HomeSharedState> {
  HomeAccountsController(HomeSharedState state) : super(state);

  final searchedAccountResults = <BasicAccountListItem>[].obs;

  @override
  bool get isResultsEmpty => searchedAccountResults.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchedAccountResults.value = [];
      return;
    }
    final response = await homeApi.searchAccount(query);
    if (response != null) {
      searchedAccountResults.value = response.accounts;
    } else {
      searchedAccountResults.value = [];
    }
  }
}
