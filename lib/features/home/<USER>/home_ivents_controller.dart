import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeIventsController extends BaseControllerWithSearch<HomeSharedState> {
  HomeIventsController(HomeSharedState state) : super(state);

  final _searchedIventsResults = <IventCardItem>[].obs;

  List<IventCardItem> get searchedIventsResults => _searchedIventsResults.toList();

  @override
  bool get isResultsEmpty => searchedIventsResults.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      _searchedIventsResults.value = [];
      return;
    }

    final response = await homeApi.searchIvent(query);
    if (response != null) {
      _searchedIventsResults.value = response.ivents;
    } else {
      _searchedIventsResults.value = [];
    }
  }

  bool isIventFavorited(String iventId) {
    final isGloballyFavorited = globalState.getIventFavoriteUpdate(iventId);
    final isLocallyFavorited = searchedIventsResults.any((e) => e.iventId == iventId && e.isFavorited);
    return isGloballyFavorited ?? isLocallyFavorited;
  }

  void toggleFavorite(String iventId) async {
    await runSafe(tag: 'toggleFavorite', () async {
      if (isIventFavorited(iventId)) {
        Get.snackbar('Favori', '${iventId} favorilerden çıkarıldı.');
        _unfavoriteIvent(iventId);
      } else {
        Get.snackbar('Favori', '${iventId} favorilere eklendi.');
        _favoriteIvent(iventId);
      }
    });
  }

  void _favoriteIvent(String iventId) async {
    await iventsApi.favoriteIventByIventId(iventId);
    final index = searchedIventsResults.indexWhere((item) => item.iventId == iventId);
    if (index != -1) {
      _searchedIventsResults[index].isFavorited = true;
      _searchedIventsResults[index] = _searchedIventsResults[index];
    }
    globalState.updateIventFavorite(iventId, true);
  }

  void _unfavoriteIvent(String iventId) async {
    await iventsApi.unfavoriteIventByIventId(iventId);
    final index = searchedIventsResults.indexWhere((item) => item.iventId == iventId);
    if (index != -1) {
      _searchedIventsResults[index].isFavorited = false;
      _searchedIventsResults[index] = _searchedIventsResults[index];
    }
    globalState.updateIventFavorite(iventId, false);
  }
}
