import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

class HomeLocationController extends BaseControllerWithSearch<HomeSharedState> {
  late final MapboxController mapboxController;

  final placeSuggestionResults = <SearchBoxSuggestFeature>[].obs;

  HomeLocationController(HomeSharedState state) : super(state) {
    mapboxController = MapboxController(authService: authService);
  }

  @override
  bool get isResultsEmpty => placeSuggestionResults.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await mapboxController.getUserLocationCoordinates();
  }

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      placeSuggestionResults.value = [];
      return;
    }

    final proximity = mapboxController.userLocationCoordinates != null
        ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
        : null;

    final response = await mapboxApi.searchBoxSuggest(
      query,
      sessionUser.sessionId,
      proximity: proximity,
      limit: 10,
    );

    if (response != null) {
      placeSuggestionResults.value = response.suggestions;
    } else {
      placeSuggestionResults.value = [];
    }
  }

  Future<void> useCurrentLocation() async {
    await runSafe(() async {
      if (mapboxController.userLocation != null) {
        state.selectedPlace.value = mapboxController.userLocation;

        Get.back();
      } else {
        await mapboxController.getUserLocationCoordinates();

        if (mapboxController.userLocation == null) {
          print('Error: Unable to get current location');
        }
      }
    });
  }

  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    await runSafe(() async {
      final result = await mapboxApi.searchBoxRetrieve(
        suggestion.mapboxId,
        sessionUser.sessionId,
      );

      if (result != null && result.features.isNotEmpty) {
        final feature = result.features.first;

        final place = IaLocationItem(
          mapboxId: feature.properties.mapboxId,
          locationId: feature.properties.mapboxId,
          name: feature.properties.name,
          address: feature.properties.fullAddress ?? feature.properties.placeFormatted,
          latitude: feature.geometry.coordinates[1],
          longitude: feature.geometry.coordinates[0],
        );

        state.selectedPlace.value = place;

        Get.back();
      }
    });
  }
}
