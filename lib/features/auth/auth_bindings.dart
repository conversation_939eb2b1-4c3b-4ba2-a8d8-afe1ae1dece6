import 'package:get/get.dart';
import 'package:ivent_app/features/auth/controllers/auth_contacts_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_phone_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_registration_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/features/auth/controllers/auth_validation_controller.dart';

class AuthBindings implements Bindings {
  @override
  void dependencies() {
    final state = Get.put(AuthSharedState());

    Get.lazyPut<AuthPhoneController>(() => AuthPhoneController(state), fenix: true);
    Get.lazyPut<AuthContactsController>(() => AuthContactsController(state), fenix: true);
    Get.lazyPut<AuthRegistrationController>(() => AuthRegistrationController(state), fenix: true);
    Get.lazyPut<AuthValidationController>(() => AuthValidationController(state), fenix: true);
  }
}
