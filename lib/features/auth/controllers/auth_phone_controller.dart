import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';

class AuthPhoneController extends BaseController<AuthSharedState> {
  AuthPhoneController(AuthSharedState state) : super(state);

  final _phoneTextController = TextEditingController();
  final _codeTextController = TextEditingController();
  final _canContinueToCodePage = false.obs;

  TextEditingController get phoneTextController => _phoneTextController;
  TextEditingController get codeTextController => _codeTextController;
  bool get canContinueToCodePage => _canContinueToCodePage.value;

  void handlePhoneValidationChanged(bool isValid) => _canContinueToCodePage.value = isValid;
  void handlePhoneNumberChanged(String phoneNumber) => state.phoneNumber = phoneNumber;
  void goToValidatePhonePage() async {
    Get.toNamed(AuthPages.validatePhone);
    await runSafe(() async {
      await authService.authApi.sendVerificationCode(
        SendVerificationCodeDto(phoneNumber: state.formattedPhoneNumber),
      );
    });
  }
}
