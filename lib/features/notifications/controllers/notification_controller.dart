import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';

class NotificationController extends BaseControllerWithSearch<FeatureSharedState> {
  final _notifications = Rxn<GetNotificationsReturn>();

  NotificationController(FeatureSharedState state) : super(state);

  GetNotificationsReturn? get notifications => _notifications.value;

  @override
  bool get isResultsEmpty => notifications?.notifications.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _getNotifications();
  }

  Future<void> _getNotifications() async {
    _notifications.value = await notificationsApi.getNotifications(limit: 50);
  }

  Future<void> refresh() async => await _getNotifications();
}
